/* reset css start */
@import url("https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;600;700&family=Roboto&family=Playfair+Display&display=swap");

html {
    scroll-behavior: smooth;
}

body {
    font-family: "Roboto", sans-serif;
    color: #6f6f6f;
    font-size: 16px;
    padding: 0;
    margin: 0;
    font-weight: 400;
    position: relative;
    line-height: 1.7;
    background-color: #f8f6ff;
}

img {
    max-width: 100%;
    height: auto;
}

/* reset css end */
h1 {
    font-size: 62px;
}

h2 {
    font-size: 32px;
}

@media (max-width: 991px) {
    h2 {
        font-size: 36px;
    }
}

@media (max-width: 575px) {
    h2 {
        font-size: 28px;
    }
}

h3 {
    font-size: 24px;
}

h4 {
    font-size: 22px;
}

@media (max-width: 767px) {
    h4 {
        font-size: 20px;
    }
}

h5 {
    font-size: 20px;
}

@media (max-width: 767px) {
    h5 {
        font-size: 18px;
    }
}

h6 {
    font-size: 18px;
}

h1, h2, h3, h4, h5, h6 {
    font-family: "Poppins", sans-serif;
    color: #525252;
    font-weight: 600;
    margin: 0;
    line-height: 1.4;
}

h1 > a, h2 > a, h3 > a, h4 > a, h5 > a, h6 > a {
    font-family: "Poppins", sans-serif;
    color: #525252;
    font-weight: 600;
    -webkit-transition: all 0.3s;
    -o-transition: all 0.3s;
    transition: all 0.3s;
    line-height: 1.4;
}

p, li, span {
    margin: 0;
}

a {
    text-decoration: none;
    display: inline-block;
    font-family: "Roboto", sans-serif;
    font-weight: 400;
}

a:hover {
    text-decoration: none;
}

/* button css start */
.cmn-btn {
    padding: 12px 35px;
    text-transform: uppercase;
    border-radius: 5px;
    -webkit-border-radius: 5px;
    -moz-border-radius: 5px;
    -ms-border-radius: 5px;
    -o-border-radius: 5px;
    background-color: #6e41ff;
    box-shadow: 0px 10px 20px 0px rgba(0, 0, 0, 0.15);
    -webkit-transition: all 0.3s;
    -o-transition: all 0.3s;
    transition: all 0.3s;
    color: #ffffff;
}

.cmn-btn:hover {
    color: #ffffff;
    background-color: #5b28ff;
}

/* button css end */
/* error-404 css start */
.error {
    position: relative;
    padding: 120px 0;
}

.error .title {
    font-size: 42px;
    margin-top: 30px;
    margin-bottom: 15px;
}

@media (max-width: 480px) {
    .error .title {
        font-size: 32px;
    }
}

.error .title b {
    font-size: 72px;
    color: #f45570;
}

.error p {
    font-size: 18px;
}

/* error-404 css end */

