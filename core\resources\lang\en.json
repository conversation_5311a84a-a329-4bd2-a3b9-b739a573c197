{"Welcome to": "Welcome to", "to": "to", "Dashboard": "Dashboard", "Username": "Username", "Password": "Password", "Remember Me": "Remember Me", "Forgot Password?": "Forgot Password?", "LOGIN": "LOGIN", "Verify Code": "Verify Code", "Please check your email and enter the verification code you got in your email.": "Please check your email and enter the verification code you got in your email.", "Verification Code": "Verification Code", "Try to send again": "Try to send again", "Submit": "Submit", "Back to Login": "Back to Login", "Recover Account": "Recover Account", "Email": "Email", "Login Here": "Login Here", "New Password": "New Password", "Re-type New Password": "Re-type New Password", "Name": "Name", "Schedule": "Schedule", "Next Run": "Next Run", "Last Run": "Last Run", "Is Running": "Is Running", "Type": "Type", "Actions": "Actions", "Running": "Running", "Pause": "Pause", "Default": "<PERSON><PERSON><PERSON>", "Customizable": "Customizable", "Action": "Action", "Run Now": "Run Now", "Play": "Play", "Edit": "Edit", "Logs": "Logs", "Are you sure to delete this cron?": "Are you sure to delete this cron?", "Delete": "Delete", "Add Cron Job": "<PERSON>d <PERSON>", "Edit Cron Job": "<PERSON> <PERSON><PERSON>", "Add": "Add", "Cron Schedule": "Cron Schedule", "Start At": "Start At", "End At": "End At", "Execution Time": "Execution Time", "Error": "Error", "Are you sure to resolved this log?": "Are you sure to resolved this log?", "Resolved": "Resolved", "Are you sure to flush all logs?": "Are you sure to flush all logs?", "Flush Logs": "Flush Logs", "Interval": "Interval", "Status": "Status", "Seconds": "Seconds", "Are you sure to enable this schedule?": "Are you sure to enable this schedule?", "Enable": "Enable", "Are you sure to disable this schedule?": "Are you sure to disable this schedule?", "Disable": "Disable", "Add Cron Schedule": "Add Cron Schedule", "Add New": "Add New", "Update Schedule": "Update Schedule", "Add Schedule": "Add Schedule", "New Version Available": "New Version Available", "Version": "Version", "What is the Update?": "What is the Update?", "Monthly Deposit & Withdraw Report": "Monthly Deposit & Withdraw Report", "Last 12 Month": "Last 12 Month", "Transactions Report": "Transactions Report", "Last 30 Days": "Last 30 Days", "Login By Browser": "Login <PERSON>rowser", "Last 30 days": "Last 30 days", "Login By OS": "Login By OS", "Login By Country": "Login By Country", "Deposit Via": "Deposit Via", "Date": "Date", "Transaction Number": "Transaction Number", "Method": "Method", "Amount": "Amount", "Charge": "Charge", "After Charge": "After Charge", "Rate": "Rate", "Payable": "Payable", "Admin Response": "Admin Response", "User Deposit Information": "User Deposit Information", "Attachment": "Attachment", "No File": "No File", "Are you sure to approve this transaction?": "Are you sure to approve this transaction?", "Approve": "Approve", "Reject": "Reject", "Reject Deposit Confirmation": "Reject Deposit Confirmation", "Are you sure to": "Are you sure to", "reject": "reject", "deposit of": "deposit of", "Reason for Rejection": "Reason for Rejection", "Successful Deposit": "Successful Deposit", "Pending Deposit": "Pending Deposit", "Rejected Deposit": "Rejected De<PERSON>t", "Initiated Deposit": "Initiated <PERSON><PERSON><PERSON><PERSON>", "Gateway | Transaction": "Gateway | Transaction", "Initiated": "Initiated", "User": "User", "Conversion": "Conversion", "charge": "charge", "Amount with charge": "Amount with charge", "Details": "Details", "Extension": "Extension", "Configure": "Configure", "Help": "Help", "Are you sure to enable this extension?": "Are you sure to enable this extension?", "Are you sure to disable this extension?": "Are you sure to disable this extension?", "Update Extension": "Update Extension", "Script": "<PERSON><PERSON><PERSON>", "Paste your script with proper key": "Paste your script with proper key", "Need Help": "Need Help", "Search": "Search", "Page Name": "Page Name", "Page Slug": "<PERSON>", "Page": "Page", "Update Now": "Update Now", "Sections": "Sections", "Drag the section to the left side you want to show on the page.": "Drag the section to the left side you want to show on the page.", "Manage Content": "Manage Content", "Slug": "Slug", "Are you sure to remove this page?": "Are you sure to remove this page?", "Add New Page": "Add <PERSON> Page", "SL": "SL", "Image": "Image", "image": "image", "Are you sure to remove this item?": "Are you sure to remove this item?", "Remove": "Remove", "Item": "<PERSON><PERSON>", "Update": "Update", "Select One": "Select One", "Import": "Import", "SEO Image": "SEO Image", "Meta Keywords": "Meta Keywords", "Separate multiple keywords by": "Separate multiple keywords by", "comma": "comma", "or": "or", "enter": "enter", "key": "key", "Meta Description": "Meta Description", "Social Title": "Social Title", "Social Description": "Social Description", "SELECTED": "SELECTED", "SELECT": "SELECT", "Template": "Template", "Get This": "Get This", "Select currency": "Select currency", "No available currency support": "No available currency support", "Add new": "Add new", "Configurations": "Configurations", "Copy": "Copy", "Global Setting for": "Global Setting for", "Are you sure to delete this gateway currency?": "Are you sure to delete this gateway currency?", "Range": "Range", "Minimum Amount": "Minimum Amount", "Maximum Amount": "Maximum Amount", "Fixed Charge": "Fixed Charge", "Percent Charge": "Percent Charge", "Currency": "<PERSON><PERSON><PERSON><PERSON>", "Symbol": "Symbol", "Configuration": "Configuration", "Gateway": "Gateway", "Supported Currency": "Supported <PERSON><PERSON><PERSON><PERSON>", "Enabled Currency": "Enabled <PERSON><PERSON><PERSON>cy", "Are you sure to enable this gateway?": "Are you sure to enable this gateway?", "Are you sure to disable this gateway?": "Are you sure to disable this gateway?", "Gateway Name": "Gateway Name", "Deposit Instruction": "Deposit Instruction", "User Data": "User Data", "Label": "Label", "KYC Form for User": "KYC Form for User", "Language Keywords of": "Language Keywords of", "Add New Key": "Add New Key", "Key": "Key", "Value": "Value", "Confirmation Alert!": "Confirmation Alert!", "Are you sure to delete this key from this language?": "Are you sure to delete this key from this language?", "No": "No", "Yes": "Yes", "Import Keywords": "Import Keywords", "Import From": "Import From", "System": "System", "Close": "Close", "Import Now": "Import Now", "While you are adding a new keyword, it will only add to this current language only. Please be careful on entering a keyword, please make sure there is no extra space. It needs to be exact and case-sensitive.": "While you are adding a new keyword, it will only add to this current language only. Please be careful on entering a keyword, please make sure there is no extra space. It needs to be exact and case-sensitive.", "Code": "Code", "Selectable": "Selectable", "Translate": "Translate", "Are you sure to remove this language from this system?": "Are you sure to remove this language from this system?", "Add New Language": "Add New Language", "Language Name": "Language Name", "Language Code": "Language Code", "Default Language": "Default Language", "SET": "SET", "UNSET": "UNSET", "Edit Language": "Edit Language", "Language Keywords": "Language Keywords", "All of the possible language keywords are available here. However, some keywords may be missing due to variations in the database. If you encounter any missing keywords, you can add them manually.": "All of the possible language keywords are available here. However, some keywords may be missing due to variations in the database. If you encounter any missing keywords, you can add them manually.", "You can import these keywords from the translate page of any language as well.": "You can import these keywords from the translate page of any language as well.", "Short Code": "Short Code", "Description": "Description", "Global Short Codes": "Global Short Codes", "Email Template": "<PERSON>ail Te<PERSON>late", "Subject": "Subject", "Email subject": "Email subject", "Send Email": "Send Email", "Message": "Message", "Your message using short-codes": "Your message using short-codes", "SMS Template": "SMS Template", "Send SMS": "Send SMS", "Email Send Method": "Email Send Method", "PHP Mail": "PHP Mail", "SMTP": "SMTP", "SendGrid API": "SendGrid API", "Mailjet API": "Mailjet API", "SMTP Configuration": "SMTP Configuration", "Host": "Host", "smtp.googlemail.com": "smtp.googlemail.com", "Port": "Port", "Available port": "Available port", "Encryption": "Encryption", "SSL": "SSL", "TLS": "TLS", "Normally your email": "Normally your email", "Normally your email password": "Normally your email password", "SendGrid API Configuration": "SendGrid API Configuration", "App Key": "App Key", "SendGrid App key": "SendGrid App key", "Mailjet API Configuration": "Mailjet API Configuration", "Api Public Key": "Api Public Key", "Mailjet Api Public Key": "Mailjet Api Public Key", "Api Secret Key": "Api Secret Key", "Mailjet Api Secret Key": "Mailjet Api Secret Key", "Test Mail Setup": "Test Mail Setup", "Sent to": "Sent to", "Email Address": "Email Address", "Send Test Mail": "Send Test Mail", "Full Name of User": "Full Name of User", "Username of User": "Username of User", "Email Sent From": "<PERSON><PERSON>", "Email address": "Email address", "Email Body": "Email Body", "Your email template": "Your email template", "SMS Sent From": "SMS Sent From", "SMS Body": "SMS Body", "Sms Send Method": "Sms Send Method", "Clickatell": "Clickatell", "Infobip": "Infobip", "Message Bird": "Message Bird", "Nexmo": "Nexmo", "Sms Broadcast": "Sms Broadcast", "Twilio": "<PERSON><PERSON><PERSON>", "Text Magic": "Text Magic", "Custom API": "Custom API", "Clickatell Configuration": "Clickatell Configuration", "API Key": "API Key", "Infobip Configuration": "Infobip Configuration", "Message Bird Configuration": "Message Bird Configuration", "Nexmo Configuration": "Nexmo Configuration", "API Secret": "API Secret", "Sms Broadcast Configuration": "Sms Broadcast Configuration", "Twilio Configuration": "<PERSON><PERSON><PERSON> Configu<PERSON>", "Account SID": "Account SID", "Auth Token": "<PERSON><PERSON>", "From Number": "From Number", "Text Magic Configuration": "Text Magic Configuration", "Apiv2 Key": "Apiv2 Key", "API URL": "API URL", "GET": "GET", "POST": "POST", "Number": "Number", "Headers": "Headers", "Headers Name": "Headers Name", "Headers Value": "Headers Value", "Body": "Body", "Body Name": "Body Name", "Body Value": "Body Value", "Test SMS Setup": "Test SMS Setup", "Mobile": "Mobile", "Send Test SMS": "Send Test SMS", "Mark All as Read": "<PERSON> as <PERSON>", "Manage Users": "Manage Users", "Active Users": "Active Users", "Banned Users": "Banned Users", "Email Unverified": "Email Unverified", "Mobile Unverified": "Mobile Unverified", "KYC Unverified": "KYC Unverified", "KYC Pending": "KYC Pending", "With Balance": "With Balance", "All Users": "All Users", "Notification to All": "Notification to All", "Payment Gateways": "Payment Gateways", "Automatic Gateways": "Automatic Gateways", "Manual Gateways": "Manual Gateways", "Deposits": "Deposits", "Pending Deposits": "Pending Deposits", "Approved Deposits": "Approved Deposits", "Successful Deposits": "Successful Deposits", "Rejected Deposits": "Rejected Deposits", "Initiated Deposits": "Initiated Deposits", "All Deposits": "All Deposits", "Withdrawals": "<PERSON><PERSON><PERSON><PERSON>", "Withdrawal Methods": "<PERSON><PERSON>wal Methods", "Pending Withdrawals": "Pending Withdrawals", "Approved Withdrawals": "Approved Withdrawals", "Rejected Withdrawals": "Rejected <PERSON>s", "All Withdrawals": "All Withdrawals", "Support Ticket": "Support Ticket", "Pending Ticket": "Pending Ticket", "Closed Ticket": "Closed Ticket", "Answered Ticket": "Answered Ticket", "All Ticket": "All Ticket", "Report": "Report", "Transaction Log": "Transaction Log", "Login History": "Login History", "Notification History": "Notification History", "Subscribers": "Subscribers", "Settings": "Settings", "General Setting": "General Setting", "Cron Job Setting": "<PERSON><PERSON>", "System Configuration": "System Configuration", "Logo & Favicon": "Logo & Favicon", "Extensions": "Extensions", "Language": "Language", "SEO Manager": "SEO Manager", "KYC Setting": "KYC Setting", "Notification Setting": "Notification Setting", "Global Template": "Global Template", "Email Setting": "<PERSON>ail <PERSON>ting", "SMS Setting": "SMS Setting", "Notification Templates": "Notification Templates", "Frontend Manager": "Frontend Manager", "Manage Templates": "Manage Templates", "Manage Pages": "Manage Pages", "Manage Section": "Manage Section", "Extra": "Extra", "Maintenance Mode": "Maintenance Mode", "GDPR Cookie": "GDPR Cookie", "Application": "Application", "Server": "Server", "Cache": "<PERSON><PERSON>", "Custom CSS": "Custom CSS", "Report & Request": "Report & Request", "V": "V", "Search here...": "Search here...", "Notification": "Notification", "You have": "You have", "unread notification": "unread notification", "No unread notification found": "No unread notification found", "View all notification": "View all notification", "Profile": "Profile", "Logout": "Logout", "Change Password": "Change Password", "Confirm Password": "Confirm Password", "Profile Setting": "Profile Setting", "Profile Information": "Profile Information", "Password Setting": "Password Setting", "Login at": "Login at", "IP": "IP", "Location": "Location", "Browser | OS": "Browser | OS", "Lookup IP": "Lookup IP", "Sent": "<PERSON><PERSON>", "Sender": "Sender", "Detail": "Detail", "Notification Details": "Notification Details", "To": "To", "Send Notification": "Send Notification", "Filter": "Filter", "TRX/Username": "TRX/Username", "All": "All", "Plus": "Plus", "Minus": "Minus", "Remark": "Remark", "Any": "Any", "Start date - End date": "Start date - End date", "TRX": "TRX", "Transacted": "Transacted", "Post Balance": "Post Balance", "Report Bug": "Report Bug", "Feature Request": "Feature Request", "Report a bug": "Report a bug", "Request for Support": "Request for Support", "User Registration": "User Registration", "If you disable this module, no one can register on this system": "If you disable this module, no one can register on this system", "Force SSL": "Force SSL", "By enabling": "By enabling", "the system will force a visitor that he/she must have to visit in secure mode. Otherwise, the site will be loaded in secure mode.": "the system will force a visitor that he/she must have to visit in secure mode. Otherwise, the site will be loaded in secure mode.", "Agree Policy": "Agree Policy", "If you enable this module, that means a user must have to agree with your system\\'s": "If you enable this module, that means a user must have to agree with your system\\'s", "policies": "policies", "during registration.": "during registration.", "Force Secure Password": "Force Secure Password", "By enabling this module, a user must set a secure password while signing up or changing the password.": "By enabling this module, a user must set a secure password while signing up or changing the password.", "KYC Verification": "KYC Verification", "If you enable": "If you enable", "KYC (Know Your Client)": "KYC (Know Your Client)", "module, users must have to submit": "module, users must have to submit", "the required data": "the required data", "Otherwise, any money out transaction will be prevented by this system.": "Otherwise, any money out transaction will be prevented by this system.", "Email Verification": "Email Verification", "users have to verify their email to access the dashboard. A 6-digit verification code will be sent to their email to be verified.": "users have to verify their email to access the dashboard. A 6-digit verification code will be sent to their email to be verified.", "Note": "Note", "Make sure that the": "Make sure that the", "Email Notification": "Email Notification", "module is enabled": "module is enabled", "If you enable this module, the system will send emails to users where needed. Otherwise, no email will be sent.": "If you enable this module, the system will send emails to users where needed. Otherwise, no email will be sent.", "So be sure before disabling this module that, the system doesn\\'t need to send any emails.": "So be sure before disabling this module that, the system doesn\\'t need to send any emails.", "Mobile Verification": "Mobile Verification", "users have to verify their mobile to access the dashboard. A 6-digit verification code will be sent to their mobile to be verified.": "users have to verify their mobile to access the dashboard. A 6-digit verification code will be sent to their mobile to be verified.", "SMS Notification": "SMS Notification", "If you enable this module, the system will send SMS to users where needed. Otherwise, no SMS will be sent.": "If you enable this module, the system will send SMS to users where needed. Otherwise, no SMS will be sent.", "So be sure before disabling this module that, the system doesn\\'t need to send any SMS.": "So be sure before disabling this module that, the system doesn\\'t need to send any SMS.", "Language Option": "Language Option", "If you enable this module, users can change the language according to their needs": "If you enable this module, users can change the language according to their needs", "Disabled": "Disabled", "Short Description": "Short Description", "From this page, you can add/update CSS for the user interface. Changing content on this page required programming knowledge.": "From this page, you can add/update CSS for the user interface. Changing content on this page required programming knowledge.", "Please do not change/edit/add anything without having proper knowledge of it. The website may misbehave due to any mistake you have made.": "Please do not change/edit/add anything without having proper knowledge of it. The website may misbehave due to any mistake you have made.", "Write Custom CSS": "Write Custom CSS", "Site Title": "Site Title", "Currency Symbol": "Currency Symbol", "Timezone": "Timezone", "Site Base Color": "Site Base Color", "Site Secondary Color": "Site Secondary Color", "If the logo and favicon are not changed after you update from this page, please": "If the logo and favicon are not changed after you update from this page, please", "clear the cache": "clear the cache", "from your browser. As we keep the filename the same after the update, it may show the old image for the cache. usually, it works after clear the cache but if you still see the old logo or favicon, it may be caused by server level or network level caching. Please clear them too.": "from your browser. As we keep the filename the same after the update, it may show the old image for the cache. usually, it works after clear the cache but if you still see the old logo or favicon, it may be caused by server level or network level caching. Please clear them too.", "Logo": "Logo", "Favicon": "Favicon", "Subscribe At": "Subscribe At", "Are you sure to remove this subscriber?": "Are you sure to remove this subscriber?", "Ticket#": "Ticket#", "Close Ticket": "Close Ticket", "Attachments": "Attachments", "Max 5 files can be uploaded. Maximum upload size is": "Max 5 files can be uploaded. Maximum upload size is", "Select your file!": "Select your file!", "Allowed File Extensions": "Allowed File Extensions", "jpg": "jpg", "jpeg": "jpeg", "png": "png", "pdf": "pdf", "doc": "doc", "docx": "docx", "Reply": "Reply", "Are you sure to delete this message?": "Are you sure to delete this message?", "Posted on": "Posted on", "Staff": "Staff", "Close Support Ticket!": "Close Support Ticket!", "Are you want to close this support ticket?": "Are you want to close this support ticket?", "Submitted By": "Submitted By", "Priority": "Priority", "Last Reply": "Last Reply", "Ticket": "Ticket", "Low": "Low", "Medium": "Medium", "High": "High", "ViserAdmin Version": "ViserAdmin Version", "Laravel Version": "Laravel Version", "Compiled views will be cleared": "Compiled views will be cleared", "Application cache will be cleared": "Application cache will be cleared", "Route cache will be cleared": "Route cache will be cleared", "Configuration cache will be cleared": "Configuration cache will be cleared", "Compiled services and packages files will be removed": "Compiled services and packages files will be removed", "Caches will be cleared": "Caches will be cleared", "Click to clear": "Click to clear", "PHP Version": "PHP Version", "Server Software": "Server Software", "Server IP Address": "Server IP Address", "Server Protocol": "Server Protocol", "HTTP Host": "HTTP Host", "Server Port": "Server Port", "To keep our support system efficient and seamless and to keep your data safe and secure, we\\'ve developed an easy to use support portal for you. We are now using that centralized system to provide support.": "To keep our support system efficient and seamless and to keep your data safe and secure, we\\'ve developed an easy to use support portal for you. We are now using that centralized system to provide support.", "Get Support": "Get Support", "The system already customized. You can\\'t update the project.": "The system already customized. You can\\'t update the project.", "Uploaded": "Uploaded", "No update patch uploaded yet.": "No update patch uploaded yet.", "Upload Update Patch": "Upload Update Patch", "If you\\'ve made any customization on this project, please don\\'t upload the updated file. It may raise issues.": "If you\\'ve made any customization on this project, please don\\'t upload the updated file. It may raise issues.", "Purchase Code": "Purchase Code", "Envato Username": "<PERSON><PERSON><PERSON>", "Files": "Files", "PHP-zip": "PHP-zip", "Extension is required": "Extension is required", "Upload": "Upload", "Balance": "Balance", "View All": "View All", "Transactions": "Transactions", "Logins": "<PERSON><PERSON>", "Notifications": "Notifications", "Login as User": "<PERSON><PERSON> as User", "KYC Data": "KYC Data", "Ban User": "Ban User", "Unban User": "Unban User", "Information of": "Information of", "First Name": "First Name", "Last Name": "Last Name", "Mobile Number": "Mobile Number", "Address": "Address", "City": "City", "State": "State", "Zip/Postal": "Zip/Postal", "Country": "Country", "Verified": "Verified", "Unverified": "Unverified", "2FA Verification": "2FA Verification", "KYC": "KYC", "Please provide positive amount": "Please provide positive amount", "If you ban this user he/she won\\'t able to access his/her dashboard.": "If you ban this user he/she won\\'t able to access his/her dashboard.", "Reason": "Reason", "Ban reason was": "Ban reason was", "Are you sure to unban this user?": "Are you sure to unban this user?", "KYC data not found": "KYC data not found", "Are you sure to reject this documents?": "Are you sure to reject this documents?", "Are you sure to approve this documents?": "Are you sure to approve this documents?", "Email-Phone": "Email-Phone", "Joined At": "Joined At", "Being Sent": "Being Sent", "Start Form": "Start Form", "Start form user": "Start form user", "Per Batch": "<PERSON>", "How many user": "How many user", "Cooling Period": "Cooling Period", "Waiting time": "Waiting time", "Notification Sending": "Notification Sending", "Don\\'t close or refresh the window till finish.": "Don\\'t close or refresh the window till finish.", "Done": "Done", "Start From": "Start From", "Ended at": "Ended at", "Email sent": "Email sent", "users": "users", "Stop": "Stop", "Notification will send via": "Notification will send via", "SMS": "SMS", "Cooling period must be greater then zero": "Cooling period must be greater then zero", "Per batch must be greater then zero": "Per batch must be greater then zero", "Notification sending will stop after this batch.": "Notification sending will stop after this batch.", "Select User": "Select User", "Number Of Top Deposited User": "Number Of Top Deposited User", "Number Of Days": "Number Of Days", "Days": "Days", "Withdraw Instruction": "Withdraw Instruction", "Input Text": "Input Text", "Textarea": "Textarea", "File": "File", "Required": "Required", "Optional": "Optional", "Withdraw Via": "Withdraw Via", "Trx Number": "Trx Number", "User Withdraw Information": "User Withdraw Information", "Approve Withdrawal Confirmation": "Approve Withdrawal Confirmation", "Have you sent": "Have you sent", "Provide the details. eg: transaction number": "Provide the details. eg: transaction number", "Reject Withdrawal Confirmation": "Reject Withdrawal Confirmation", "Reason of Rejection": "Reason of Rejection", "Withdraw Limit": "Withdraw Limit", "Are you sure to disable this method?": "Are you sure to disable this method?", "Are you sure to enable this method?": "Are you sure to enable this method?", "Amount after charge": "Amount after charge", "Back": "Back", "Generate Form": "Generate Form", "Form Type": "Form Type", "Text": "Text", "Select": "Select", "Checkbox": "Checkbox", "Radio": "Radio", "Is Required": "Is Required", "Form Label": "Form Label", "Supported Files:": "Supported Files:", "Image will be resized into": "Image will be resized into", "px": "px", "Start Date - End Date": "Start Date - End Date", "Supported mimes": "Supported mimes", "0": "404", "Page not found": "Page not found", "page you are looking for doesn\\'t exit or an other error ocurred": "page you are looking for doesn\\'t exit or an other error ocurred", "or temporarily unavailable.": "or temporarily unavailable.", "Go to Home": "Go to Home", "1": "419", "Sorry your session has expired.": "Sorry your session has expired.", "Please go back and refresh your browser and try again": "Please go back and refresh your browser and try again", "Captcha": "<PERSON><PERSON>", "Home": "Home", "contact": "contact", "login": "login", "register": "register", "ViserLab": "ViserLab", "Github": "<PERSON><PERSON><PERSON>", "Laravel": "<PERSON><PERSON>", "learn more": "learn more", "Allow": "Allow", "Toggle navigation": "Toggle navigation", "Create New": "Create New", "Deposit": "<PERSON><PERSON><PERSON><PERSON>", "Deposit Money": "Deposit Money", "Withdraw": "Withdraw", "Withdraw Money": "Withdraw Money", "2FA Security": "2FA Security", "You are banned": "You are banned", "Verify Email Address": "Verify Em<PERSON> Address", "A 6 digit verification code sent to your email address": "A 6 digit verification code sent to your email address", "If you don\\'t get any code": "If you don\\'t get any code", "Try again": "Try again", "Verify Mobile Number": "Verify Mobile Number", "A 6 digit verification code sent to your mobile number": "A 6 digit verification code sent to your mobile number", "Login": "<PERSON><PERSON>", "Username or Email": "Username or Email", "Forgot your password?": "Forgot your password?", "Don\\'t have any account?": "Don\\'t have any account?", "Register": "Register", "Please check including your Junk/Spam Folder. if not found, you can": "Please check including your Junk/Spam Folder. if not found, you can", "To recover your account please provide your email or username to find your account.": "To recover your account please provide your email or username to find your account.", "Email or Username": "Email or Username", "Reset Password": "Reset Password", "Your account is verified successfully. Now you can change your password. Please enter a strong password and don\\'t share it with anyone.": "Your account is verified successfully. Now you can change your password. Please enter a strong password and don\\'t share it with anyone.", "Reference by": "Reference by", "E-Mail Address": "E-Mail Address", "I agree with": "I agree with", "Already have an account?": "Already have an account?", "You are with us": "You are with us", "You already have an account please Login": "You already have an account please Login", "KYC Verification required": "KYC Verification required", "Click Here to Verify": "Click Here to Verify", "KYC Verification pending": "KYC Verification pending", "See KYC Data": "See KYC Data", "Search by transactions": "Search by transactions", "Admin Feedback": "<PERSON><PERSON>", "KYC Form": "KYC Form", "Current Password": "Current Password", "Authorize Net": "Authorize Net", "Name on Card": "Name on Card", "Card Number": "Card Number", "Expiration Date": "Expiration Date", "CVC Code": "CVC Code", "Checkout.com": "Checkout.com", "Payment Preview": "Payment Preview", "PLEASE SEND EXACTLY": "PLEASE SEND EXACTLY", "TO": "TO", "SCAN TO SEND": "SCAN TO SEND", "Select Gateway": "Select Gateway", "Limit": "Limit", "In": "In", "Conversion with": "Conversion with", "and final value will Show on next step": "and final value will Show on next step", "Conversion Rate": "Conversion Rate", "Flutterwave": "Flutterwave", "You have to pay": "You have to pay", "You will get": "You will get", "Pay Now": "Pay Now", "You have requested": "You have requested", "Please pay": "Please pay", "for successful payment": "for successful payment", "Please follow the instruction below": "Please follow the instruction below", "NMI": "NMI", "Paystack": "Paystack", "Razorpay": "Razorpay", "Stripe Hosted": "Stripe Hosted", "Stripe Storefront": "Stripe Storefront", "Deposit with Stripe": "Deposit with <PERSON><PERSON>", "Voguepay": "Voguepay", "E-mail Address": "E-mail Address", "Zip Code": "Zip Code", "My Support Ticket": "My Support Ticket", "Max 5 files can be uploaded": "Max 5 files can be uploaded", "Maximum upload size is": "Maximum upload size is", "New Ticket": "New Ticket", "Are you sure to close this ticket?": "Are you sure to close this ticket?", "Trx": "Trx", "Add Your Account": "Add Your Account", "Use the QR code or setup key on your Google Authenticator app to add your account.": "Use the QR code or setup key on your Google Authenticator app to add your account.", "Setup Key": "Setup Key", "Google Authenticator is a multifactor app for mobile devices. It generates timed codes used during the 2-step verification process. To use Google Authenticator, install the Google Authenticator application on your mobile device.": "Google Authenticator is a multifactor app for mobile devices. It generates timed codes used during the 2-step verification process. To use Google Authenticator, install the Google Authenticator application on your mobile device.", "Download": "Download", "Disable 2FA Security": "Disable 2FA Security", "Google Authenticatior OTP": "Google Authenticatior OTP", "Enable 2FA Security": "Enable 2FA Security", "Receivable": "Receivable", "Google Authenticator Code": "Google Authenticator Code", "Latest News": "Latest News", "Lorem ipsum dolor sit, amet consectetur adipisicing elit. Doloribus necessitatibus repudiandae porro reprehenderit, beatae perferendis repellat quo ipsa omnis, vitae!": "Lorem ipsum dolor sit, amet consectetur adipisicing elit. Doloribus necessitatibus repudiandae porro reprehenderit, beatae perferendis repellat quo ipsa omnis, vitae!", "fdg sdfgsdf g ggg": "fdg sdfgsdf g ggg", "------": "------", "this is a test blog 2": "this is a test blog 2", "aewf asdf": "aewf asdf", "asdf asdf": "asdf asdf", "Auctor gravida vestibulu": "<PERSON><PERSON> gravida vestibulu", "55f55": "55f55", "5555f": "5555f", "5555h": "5555h", "5555a": "5555a", "5555s": "5555s", "5555qqq": "5555qqq", "Register New Account": "Register New Account", "Facebook": "Facebook", "https://www.google.com/": "https://www.google.com/", "asdf": "asdf", "withdraw": "withdraw", "asdfasdf": "asdfasdf", "asdfasdfasdfasdf": "asdfasdfasdfasdf", "deposit": "deposit", "asdf fffff": "asdf fffff", "2": "555", "We may use cookies or any other tracking technologies when you visit our website, including any other media form, mobile website, or mobile application related or connected to help customize the Site and improve your experience.": "We may use cookies or any other tracking technologies when you visit our website, including any other media form, mobile website, or mobile application related or connected to help customize the Site and improve your experience.", "3": "1", "Privacy Policy": "Privacy Policy", "Terms of Service": "Terms of Service", "adsf": "adsf", "S.N.": "S.N.", "Are you sure to disable this brand?": "Are you sure to disable this brand?", "Are you sure to enable this brand?": "Are you sure to enable this brand?", "Add New Brand": "Add New Brand", "Update Brand": "Update Brand", "Payment Via": "Payment Via", "User Payment Information": "User Payment Information", "Reject Payment Confirmation": "Reject Payment Confirmation", "payment of": "payment of", "Successful Payment": "Successful Payment", "Pending Payment": "Pending Payment", "Rejected Payment": "Rejected Payment", "Initiated Payment": "Initiated Payment", "KYC Form for Vehicle Store": "KYC Form for Vehicle Store", "Vehicle Types": "Vehicle Types", "Vehicle Classes": "Vehicle Classes", "Zones": "Zones", "Brand": "Brand", "Manage Stores": "Manage Stores", "All Stores": "All Stores", "Pending Stores": "Pending Stores", "Approved Stores": "Approved Stores", "Rejected Stores": "Rejected Stores", "Manage Vehicles": "Manage Vehicles", "All Vehicles": "All Vehicles", "Pending Vehicles": "Pending Vehicles", "Approved Vehicles": "Approved Vehicles", "Rejected Vehicles": "Rejected Vehicles", "Manage Rentals": "Manage Rentals", "All Rentals": "All Rentals", "Pending Rentals": "Pending Rentals", "Approved Rental": "Approved Rental", "Ongoing Rental": "Ongoing Rental", "Completed Rental": "Completed Rental", "Cancelled Rental": "Cancelled <PERSON>", "Payments": "Payments", "Pending Payments": "Pending Payments", "Approved Payments": "Approved Payments", "Successful Payments": "Successful Payments", "Rejected Payments": "Rejected Payments", "Initiated Payments": "Initiated Payments", "All Payments": "All Payments", "Social Credentials": "Social Credentials", "Vehicle Store": "Vehicle Store", "requested for rental": "requested for rental", "User Profile": "User Profile", "Store": "Store", "Joined": "Joined", "Vehicle Owner": "Vehicle Owner", "Owner Profile": "Owner Profile", "Rental Price": "Rental Price", "CC": "CC", "BHP": "BHP", "Speed": "Speed", "Cylinder": "<PERSON><PERSON><PERSON>", "Total Run": "Total Run", "km": "km", "More Information": "More Information", "Pick Up Zone": "Pick Up Zone", "Pick Up Location": "Pick Up Location", "Drop Off Zone": "Drop Off Zone", "Drop Off Location": "Drop Off Location", "Drop Store": "Drop Store", "Start Date": "Start Date", "End Date": "End Date", "Rent No": "Rent No", "Pick Up | Drop Zone": "Pick Up | Drop Zone", "Price": "Price", "Maximum Image can be Uploaded": "Maximum Image can be Uploaded", "Qty": "Qty", "Rental Charge": "Rental Charge", "%": "%", "Google Map API Key": "Google Map API Key", "Dark Logo": "Dark Logo", "White Logo": "White Logo", "Title": "Title", "Client ID": "Client ID", "Enabled": "Enabled", "Are you sure that you want to enable this login credential?": "Are you sure that you want to enable this login credential?", "Are you sure that you want to disable login credential?": "Are you sure that you want to disable login credential?", "Update Credential": "Update Credential", "Client Secret": "Client Secret", "Callback URL": "Callback URL", "User Information": "User Information", "Fullname": "Fullname", "Store Status": "Store Status", "Store Information": "Store Information", "Are you sure to approve this store?": "Are you sure to approve this store?", "Zone": "Zone", "Vehicle store data not found": "Vehicle store data not found", "Reject Store Confirmation": "Reject Store Confirmation", "Are you sure to reject this store?": "Are you sure to reject this store?", "S.N": "S.N", "Store-Location": "Store-Location", "Vehicle": "Vehicle", "Are you sure to disable this vehicle class?": "Are you sure to disable this vehicle class?", "Are you sure to enable this vehicle class?": "Are you sure to enable this vehicle class?", "Vehicle Type": "Vehicle Type", "Add New Vehicle Class": "Add New Vehicle Class", "Update Vehicle Class": "Update Vehicle Class", "Take Action": "Take Action", "Are you sure to approve this vehicle?": "Are you sure to approve this vehicle?", "DAY": "DAY", "Vehicle Information": "Vehicle Information", "Class": "Class", "Pick-up Point": "Pick-up Point", "Drop Point": "Drop Point", "Color": "Color", "Mileage": "Mileage", "Year": "Year", "Identification Number": "Identification Number", "Transmission Type": "Transmission Type", "Condition": "Condition", "Fuel Type": "Fuel Type", "Seat": "<PERSON><PERSON>", "Reject Vehicle Confirmation": "Reject Vehicle Confirmation", "vehicle of": "vehicle of", "Store | Username": "Store | Username", "Identification NO.": "Identification NO.", "Approval Status": "Approval Status", "Are you sure to disable this vehicle?": "Are you sure to disable this vehicle?", "Are you sure to enable this vehicle?": "Are you sure to enable this vehicle?", "Image | Name": "Image | Name", "Manage Class": "Manage Class", "YES": "YES", "NO": "NO", "Add New Vehicle Type": "Add New Vehicle Type", "Update Vehicle Type": "Update Vehicle Type", "Mark Your Zone": "Mark Your Zone", "Delete Selected Area": "Delete Selected Area", "Are you sure to disable this zone?": "Are you sure to disable this zone?", "Are you sure to enable this zone?": "Are you sure to enable this zone?", "4": "404", "page you are looking for doesn\\'t exist or an other error ocurred": "page you are looking for doesn\\'t exist or an other error ocurred", "5": "419", "Captcha field is required.": "Captcha field is required.", "Share This": "Share This", "Latest Blog": "Latest Blog", "Phone": "Phone", "Office": "Office", "Day": "Day", "Rent Now": "Rent Now", "Quick Links": "Quick Links", "Vehicles": "Vehicles", "Contact With Us": "Contact With Us", "Subscribe": "Subscribe", "Copyright": "Copyright", "All rights reserved.": "All rights reserved.", "Blog": "Blog", "Contact": "Contact", "profile": "profile", "Manage Vehicle": "Manage Vehicle", "Add Vehicle": "Add Vehicle", "Manage Rental": "Manage Rental", "Pending": "Pending", "Approved": "Approved", "Ongoing": "Ongoing", "Completed": "Completed", "Cancelled": "Cancelled", "My Rented History": "My Rented History", "Reviews": "Reviews", "Payment History": "Payment History", "Withdraw History": "Withdraw History", "Open New Ticket": "Open New Ticket", "My Tickets": "My Tickets", "Google": "Google", "Linkedin": "Linkedin", "Select Vehicle": "Select Vehicle", "Pick Up": "Pick Up", "Drop off": "Drop off", "Vehicle Store Required": "Vehicle Store Required", "Vehicle Store Pending": "Vehicle Store Pending", "See Vehicle Store Data": "See Vehicle Store Data", "Vehicle Availability": "Vehicle Availability", "Total Vehicles": "Total Vehicles", "Total Balance": "Total Balance", "Total Income": "Total Income", "Total Withdrawan": "Total Withdrawan", "Latest Rental Status": "Latest Rental Status", "Rent NO.": "Rent NO.", "Rented By": "Rented By", "Expired At": "Expired At", "Are you sure to approve this rental request?": "Are you sure to approve this rental request?", "Are you sure to cancel this rental request?": "Are you sure to cancel this rental request?", "Cancel": "Cancel", "Are you sure this rental will be ongoing?": "Are you sure this rental will be ongoing?", "On going": "On going", "Are you sure this rental will be completed?": "Are you sure this rental will be completed?", "Complete": "Complete", "Rental Number": "Rental Number", "Requested for rent": "Requested for rent", "Pickup Store": "Pickup Store", "Search by rent no...": "Search by rent no...", "Pick Up Store": "Pick Up Store", "Drop Off Store": "Drop Off Store", "Total": "Total", "Pick Up | Zone": "Pick Up | Zone", "Reviewed": "Reviewed", "Review": "Review", "Rating": "Rating", "Write here": "Write here", "Are you sure to remove this review?": "Are you sure to remove this review?", "No review yet": "No review yet", "Reply here...": "Reply here...", "Previous Replies": "Previous Replies", "Supported files:": "Supported files:", ".jpeg": ".jpeg", ".jpg": ".jpg", ".png": ".png", "Vehicle Class": "Vehicle Class", "Model": "Model", "CC (Cubic Centimeters)": "CC (Cubic Centimeters)", "BHP (Brake Horsepower)": "BHP (Brake Horsepower)", "Km/h": "Km/h", "Km/l": "Km/l", "New": "New", "Used": "Used", "Automatic": "Automatic", "Manual": "Manual", "Petrol": "Petrol", "Gasholine": "Gasholine", "Diesel": "Diesel", "Electric": "Electric", "Number of Seats": "Number of Seats", "KM": "KM", "Rental Price Per Day": "Rental Price Per Day", "Images": "Images", "Maximum": "Maximum", "images can be uploaded": "images can be uploaded", "File size will be": "File size will be", "Store Image": "Store Image", "Payment Method": "Payment Method", "Model Year": "Model Year", "Seater": "<PERSON><PERSON>", "PRICE FOR A": "PRICE FOR A", "1 Day": "1 Day", "Reserve Now": "Reserve Now", "Login Now": "Login Now", "Rental Details": "Rental Details", "Pick-up Point & Zone": "Pick-up Point & Zone", "No Description Yet!": "No Description Yet!", "No Review Yet!": "No Review Yet!", "Store Location": "Store Location", "Confirm Reservation": "Confirm Reservation", "Login to Your Account": "Login to Your Account", "Filter Vehicles": "Filter Vehicles", "Low to High": "Low to High", "High to Low": "High to Low", "https://www.facebook.com/": "https://www.facebook.com/", "Twitter": "Twitter", "https://www.twitter.com/": "https://www.twitter.com/", "https://www.linkedin.com/": "https://www.linkedin.com/", "Explore our platform for hassle-free vehicle rentals. Book your ride easily and embark on unforgettable journeys with confidence.": "Explore our platform for hassle-free vehicle rentals. Book your ride easily and embark on unforgettable journeys with confidence.", "Subscribe to our mailing list for the latest updates!": "Subscribe to our mailing list for the latest updates!", "Instagram": "Instagram", "https://www.instagram.com/": "https://www.instagram.com/", "Contact Us": "Contact Us", "Reach out to us for any inquiries or assistance.": "Reach out to us for any inquiries or assistance.", "https://www.google.com/maps/embed?pb=!1m18!1m12!1m3!1d2993.*************!2d2.****************!3d41.38089999639698!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0x12a498f576297baf%3A0x44f65330fe1b04b9!2sSpotify%20Camp%20Nou!5e0!3m2!1sen!2sbd!4v1717222893198!5m2!1sen!2sbd": "https://www.google.com/maps/embed?pb=!1m18!1m12!1m3!1d2993.*************!2d2.****************!3d41.38089999639698!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0x12a498f576297baf%3A0x44f65330fe1b04b9!2sSpotify%20Camp%20Nou!5e0!3m2!1sen!2sbd!4v1717222893198!5m2!1sen!2sbd", "123 Sample St, Sydney NSW 2000 AU": "123 Sample St, Sydney NSW 2000 AU", "<EMAIL>": "<EMAIL>", "+1(555)000-0000": "+1(555)000-0000", "Start Your Journey,": "Start Your Journey,", "Your Ultimate Vehicle Rental Platform": "Your Ultimate Vehicle Rental Platform", "Rent a luxury vehicle for as low as $150 daily from our extensive network of over 100 premium locations.": "Rent a luxury vehicle for as low as $150 daily from our extensive network of over 100 premium locations.", "/contact": "/contact", "How it’s Works": "How it’s Works", "Fast and Convenient Vehicle Rental": "Fast and Convenient Vehicle Rental", "Choose a vehicle": "Choose a vehicle", "Explore our diverse fleet and find the perfect vehicle to suit your needs, preferences, and budget effortlessly": "Explore our diverse fleet and find the perfect vehicle to suit your needs, preferences, and budget effortlessly", "Pick location & date": "Pick location & date", "Select your preferred location and dates seamlessly to ensure a smooth and hassle-free rental experience tailored to your schedule": "Select your preferred location and dates seamlessly to ensure a smooth and hassle-free rental experience tailored to your schedule", "Make a booking": "Make a booking", "Easily confirm your reservation online with just a few clicks, securing your chosen vehicle for your desired dates instantly.": "Easily confirm your reservation online with just a few clicks, securing your chosen vehicle for your desired dates instantly.", "Sit back & relax": "Sit back & relax", "Once booked, sit back and relax as we prepare your chosen vehicle for a seamless and enjoyable rental experience.": "Once booked, sit back and relax as we prepare your chosen vehicle for a seamless and enjoyable rental experience.", "Enjoy Your Ride": "Enjoy Your Ride", "Our Fleet of Vehicles": "Our Fleet of Vehicles", "Our Features": "Our Features", "Experience seamless vehicle rental with our hassle-free service.": "Experience seamless vehicle rental with our hassle-free service.", "Free Delivery": "Free Delivery", "Enjoy the convenience of complimentary delivery for your rented vehicle at no extra cost.": "Enjoy the convenience of complimentary delivery for your rented vehicle at no extra cost.", "Any Pickup Location": "Any Pickup Location", "Choose any location for convenient vehicle pickup, tailored to your needs and preferences": "Choose any location for convenient vehicle pickup, tailored to your needs and preferences", "Customers Satisfaction": "Customers Satisfaction", "Our priority is ensuring customer satisfaction through excellent service, reliability, and personalized attention to your needs.": "Our priority is ensuring customer satisfaction through excellent service, reliability, and personalized attention to your needs.", "Easier & Faster Bookings": "Easier & Faster Bookings", "Simplify and expedite the booking process with our platform for quicker access to your desired vehicle.": "Simplify and expedite the booking process with our platform for quicker access to your desired vehicle.", "Our Services": "Our Services", "What Makes Us Different?": "What Makes Us Different?", "Pick up and Drop": "Pick up and Drop", "Maintained Cars": "Maintained Cars", "Professional Staff": "Professional Staff", "Excellent Prices": "Excellent Prices", "Easy Booking": "Easy Booking", "Feedback": "<PERSON><PERSON><PERSON>", "Our Customer Reviews": "Our Customer Reviews", "Emily Johnson": "<PERSON>", "Marketing Manager": "Marketing Manager", "Bright Ideas Inc.": "Bright Ideas Inc.", "Renting a vehicle from  Bright Ideas Inc was a breeze! Their easy booking process and excellent prices made my trip stress-free.": "Renting a vehicle from  Bright Ideas Inc was a breeze! Their easy booking process and excellent prices made my trip stress-free.", "David Smith": "<PERSON>", "CEO": "CEO", "Summit Solutions": "Summit Solutions", "The professionalism of the Summit Solutions staff stood out to me. They helped me find the perfect vehicle for my needs at an unbeatable price.": "The professionalism of the Summit Solutions staff stood out to me. They helped me find the perfect vehicle for my needs at an unbeatable price.", "Sophia Lee": "<PERSON>", "Freelance": "Freelance", "Photography LLC": "Photography LLC", "I rely on Photography LLC for all my travel needs. Their convenient pick-up and drop-off services make my photography assignments a breeze": "I rely on Photography LLC for all my travel needs. Their convenient pick-up and drop-off services make my photography assignments a breeze", "Michael Anderson": "<PERSON>", "Traveller": "Traveller", "Travel Enthusiast": "Travel Enthusiast", "I've tried many rental services, but none compare to Travel Enthusiast. Their excellent prices and easy booking process keep me coming back every time!": "I've tried many rental services, but none compare to Travel Enthusiast. Their excellent prices and easy booking process keep me coming back every time!", "200+ sports & luxury vehicles": "200+ sports & luxury vehicles", "DRIVE YOUR DREAM VEHICLE TODAY": "DRIVE YOUR DREAM VEHICLE TODAY", "Rent a sports or luxury car , delivered directly to your hotel in Dubai": "Rent a sports or luxury car , delivered directly to your hotel in Dubai", "https://url8.viserlab.com/justhire/video.mp4": "https://url8.viserlab.com/justhire/video.mp4", "Book Ride": "Book Ride", "/user/login": "/user/login", "Do You Have": "Do You Have", "Any Questions?": "Any Questions?", "How can I make a reservation?": "How can I make a reservation?", "Making a reservation is easy! Simply visit our website or download our mobile app, choose your desired vehicle, select your pickup and drop-off locations and dates, and complete the booking process.": "Making a reservation is easy! Simply visit our website or download our mobile app, choose your desired vehicle, select your pickup and drop-off locations and dates, and complete the booking process.", "Do you offer delivery and pickup services?": "Do you offer delivery and pickup services?", "Yes, we do! We offer convenient delivery and pickup services to various locations, including airports, hotels, and more. Just let us know your preferred location, and we'll take care of the rest.": "Yes, we do! We offer convenient delivery and pickup services to various locations, including airports, hotels, and more. Just let us know your preferred location, and we'll take care of the rest.", "What are your rental rates?": "What are your rental rates?", "Our rental rates vary depending on the vehicle type, rental duration, and other factors. You can find our competitive rates displayed on our website or by contacting our customer support team for a personalized quote.": "Our rental rates vary depending on the vehicle type, rental duration, and other factors. You can find our competitive rates displayed on our website or by contacting our customer support team for a personalized quote.", "What documents do I need to rent a vehicle?": "What documents do I need to rent a vehicle?", "To rent a vehicle, you'll typically need a valid driver's license, a credit card for payment and security deposit, and proof of insurance. Additional requirements may vary depending on your location and the type of vehicle you're renting.": "To rent a vehicle, you'll typically need a valid driver's license, a credit card for payment and security deposit, and proof of insurance. Additional requirements may vary depending on your location and the type of vehicle you're renting.", "Is there an age requirement for renting a vehicle?": "Is there an age requirement for renting a vehicle?", "Yes, the minimum age requirement for renting a vehicle is usually 21 years old. However, some locations may have higher age requirements or additional restrictions for certain vehicle types.": "Yes, the minimum age requirement for renting a vehicle is usually 21 years old. However, some locations may have higher age requirements or additional restrictions for certain vehicle types.", "What happens if I need to cancel my reservation?": "What happens if I need to cancel my reservation?", "We understand that plans can change, which is why we offer flexible cancellation policies. Depending on the timing of your cancellation, there may be applicable fees. Please refer to our terms and conditions or contact our customer support team for assistance with cancellations.": "We understand that plans can change, which is why we offer flexible cancellation policies. Depending on the timing of your cancellation, there may be applicable fees. Please refer to our terms and conditions or contact our customer support team for assistance with cancellations.", "Rentaly customer care is here to help you anytime.": "Rentaly customer care is here to help you anytime.", "Call us for further information": "Call us for further information", "CALL US NOW": "CALL US NOW", "+************": "+************", "Contact US": "Contact US", "About Company": "About Company", "You start the engine and your adventure begins": "You start the engine and your adventure begins", "Certain but she but shyness why cottage. Guy the put instrument sir entreaties affronting. Pretended exquisite see cordially the you. Weeks quiet do vexed or whose. Motionless if no to affronting imprudence no precaution. My indulged as disposal strongly attended.": "Certain but she but shyness why cottage. Guy the put instrument sir entreaties affronting. Pretended exquisite see cordially the you. Weeks quiet do vexed or whose. Motionless if no to affronting imprudence no precaution. My indulged as disposal strongly attended.", "6": "20", "Car Types": "Car Types", "Rental Outlet": "Rental Outlet", "Service Point": "Service Point", "Save big with our cheap car rental!": "Save big with our cheap car rental!", "Top outlet. Local Suppliers. 24/7 Support.": "Top outlet. Local Suppliers. 24/7 Support.", "/": "/", "Why Choose Us": "Why Choose Us", "Experience Excellence in Every Mile": "Experience Excellence in Every Mile", "Discover unparalleled convenience, reliability, and value with our exceptional service. From hassle-free bookings to top-notch vehicles, we're your ultimate travel partner.": "Discover unparalleled convenience, reliability, and value with our exceptional service. From hassle-free bookings to top-notch vehicles, we're your ultimate travel partner.", "Find Deal": "Find Deal", "Expert Staff": "Expert Staff", "Convenient Booking": "Convenient Booking", "Reliable Vehicles": "Reliable Vehicles", "Affordable Rates": "Affordable Rates", "Exceptional Service": "Exceptional Service", "Easy Process": "Easy Process", "Blogs": "Blogs", "Our Latest Blogs": "Our Latest Blogs", "Exploring the Unknown: Embracing the Joy of Travel": "Exploring the Unknown: Embracing the Joy of Travel", "Embracing the Journey: Finding Serenity on the Open Roa": "Embracing the Journey: Finding Ser<PERSON>ty on the Open Roa", "Embracing Adventure Beyond the Beaten Path": "Embracing Adventure Beyond the Beaten Path", "Discovering the World One Journey at a Time": "Discovering the World One Journey at a Time", "Welcome Back": "Welcome Back", "Create Your Account": "Create Your Account", "Join Us Today for Exclusive Benefits": "Join <PERSON> Today for Exclusive Benefits", "Your vehicle store registration is incomplete. Please provide necessary details for approval.": "Your vehicle store registration is incomplete. Please provide necessary details for approval.", "Your vehicle store registration is pending. All listings are disabled until approval.": "Your vehicle store registration is pending. All listings are disabled until approval.", "If you wish to update your vehicle store, please contact the admin via support ticket for assistance.": "If you wish to update your vehicle store, please contact the admin via support ticket for assistance.", "Your account verification is pending approval. Certain features may be limited until KYC is complete.": "Your account verification is pending approval. Certain features may be limited until KYC is complete.", "Your account verification is pending. Please complete KYC by providing the required documents for approval.": "Your account verification is pending. Please complete KYC by providing the required documents for approval.", "Curabitur at lacus ac velit ornare lobortis. Suspendisse feugiat.": "Curabitur at lacus ac velit ornare lobortis. Suspendisse feugiat.", "Praesent adipiscing. Cras dapibus.": "Praesent adipiscing. Cras dapibus."}