/* Font Family*/
@import url("https://fonts.googleapis.com/css2?family=Inter:wght@100..900&family=Urbanist:ital,wght@0,100..900;1,100..900&display=swap");

/* ========================= Css Variables Start ======================== */
:root {
  /* Font Family */
  --heading-font: "Urbanist", sans-serif;
  --body-font: "Inter", sans-serif;
  /* ========================= Theme Color Start ============================= */
  /* Base Color */
  --base-h: 24;
  --base-s: 98%;
  --base-l: 56%;
  --base: var(--base-h) var(--base-s) var(--base-l);
  --base-d-100: var(--base-h) var(--base-s)
    calc(var(--base-l) - var(--base-l) * 0.1);
  --base-d-200: var(--base-h) var(--base-s)
    calc(var(--base-l) - var(--base-l) * 0.2);
  --base-d-300: var(--base-h) var(--base-s)
    calc(var(--base-l) - var(--base-l) * 0.3);
  --base-d-400: var(--base-h) var(--base-s)
    calc(var(--base-l) - var(--base-l) * 0.4);
  --base-d-500: var(--base-h) var(--base-s)
    calc(var(--base-l) - var(--base-l) * 0.5);
  --base-d-600: var(--base-h) var(--base-s)
    calc(var(--base-l) - var(--base-l) * 0.6);
  --base-d-700: var(--base-h) var(--base-s)
    calc(var(--base-l) - var(--base-l) * 0.7);
  --base-d-800: var(--base-h) var(--base-s)
    calc(var(--base-l) - var(--base-l) * 0.8);
  --base-d-900: var(--base-h) var(--base-s)
    calc(var(--base-l) - var(--base-l) * 0.9);
  --base-l-100: var(--base-h) calc(var(--base-s))
    calc(var(--base-l) + (100% - var(--base-l)) * 0.1);
  --base-l-200: var(--base-h) calc(var(--base-s))
    calc(var(--base-l) + (100% - var(--base-l)) * 0.2);
  --base-l-300: var(--base-h) calc(var(--base-s))
    calc(var(--base-l) + (100% - var(--base-l)) * 0.3);
  --base-l-400: var(--base-h) calc(var(--base-s))
    calc(var(--base-l) + (100% - var(--base-l)) * 0.4);
  --base-l-500: var(--base-h) calc(var(--base-s))
    calc(var(--base-l) + (100% - var(--base-l)) * 0.5);
  --base-l-600: var(--base-h) calc(var(--base-s))
    calc(var(--base-l) + (100% - var(--base-l)) * 0.6);
  --base-l-700: var(--base-h) calc(var(--base-s))
    calc(var(--base-l) + (100% - var(--base-l)) * 0.7);
  --base-l-800: var(--base-h) calc(var(--base-s))
    calc(var(--base-l) + (100% - var(--base-l)) * 0.8);
  --base-l-900: var(--base-h) calc(var(--base-s))
    calc(var(--base-l) + (100% - var(--base-l)) * 0.9);
  /* Base Two Color */
  --base-two-h: 24;
  --base-two-s: 75%;
  --base-two-l: 11%;
  --base-two: var(--base-two-h) var(--base-two-s) var(--base-two-l);
  --base-two-d-100: var(--base-two-h) var(--base-two-s)
    calc(var(--base-two-l) - var(--base-two-l) * 0.1);
  --base-two-d-200: var(--base-two-h) var(--base-two-s)
    calc(var(--base-two-l) - var(--base-two-l) * 0.2);
  --base-two-d-300: var(--base-two-h) var(--base-two-s)
    calc(var(--base-two-l) - var(--base-two-l) * 0.3);
  --base-two-d-400: var(--base-two-h) var(--base-two-s)
    calc(var(--base-two-l) - var(--base-two-l) * 0.4);
  --base-two-d-500: var(--base-two-h) var(--base-two-s)
    calc(var(--base-two-l) - var(--base-two-l) * 0.5);
  --base-two-d-600: var(--base-two-h) var(--base-two-s)
    calc(var(--base-two-l) - var(--base-two-l) * 0.6);
  --base-two-d-700: var(--base-two-h) var(--base-two-s)
    calc(var(--base-two-l) - var(--base-two-l) * 0.7);
  --base-two-d-800: var(--base-two-h) var(--base-two-s)
    calc(var(--base-two-l) - var(--base-two-l) * 0.8);
  --base-two-d-900: var(--base-two-h) var(--base-two-s)
    calc(var(--base-two-l) - var(--base-two-l) * 0.9);
  --base-two-l-100: var(--base-two-h) calc(var(--base-two-s))
    calc(var(--base-two-l) + (100% - var(--base-two-l)) * 0.1);
  --base-two-l-200: var(--base-two-h) calc(var(--base-two-s))
    calc(var(--base-two-l) + (100% - var(--base-two-l)) * 0.2);
  --base-two-l-300: var(--base-two-h) calc(var(--base-two-s))
    calc(var(--base-two-l) + (100% - var(--base-two-l)) * 0.3);
  --base-two-l-400: var(--base-two-h) calc(var(--base-two-s))
    calc(var(--base-two-l) + (100% - var(--base-two-l)) * 0.4);
  --base-two-l-500: var(--base-two-h) calc(var(--base-two-s))
    calc(var(--base-two-l) + (100% - var(--base-two-l)) * 0.5);
  --base-two-l-600: var(--base-two-h) calc(var(--base-two-s))
    calc(var(--base-two-l) + (100% - var(--base-two-l)) * 0.6);
  --base-two-l-700: var(--base-two-h) calc(var(--base-two-s))
    calc(var(--base-two-l) + (100% - var(--base-two-l)) * 0.7);
  --base-two-l-800: var(--base-two-h) calc(var(--base-two-s))
    calc(var(--base-two-l) + (100% - var(--base-two-l)) * 0.8);
  --base-two-l-900: var(--base-two-h) calc(var(--base-two-s))
    calc(var(--base-two-l) + (100% - var(--base-two-l)) * 0.9);
  /* ========================= Theme Color End ============================= */
  /* ========================= Color Variables Start =========================== */
  --white-h: 0;
  --white-s: 0%;
  --white-l: 100%;
  --white: var(--white-h) var(--white-s) var(--white-l);
  --white-d-100: var(--white-h) var(--white-s)
    calc(var(--white-l) - var(--white-l) * 0.1);
  --white-d-200: var(--white-h) var(--white-s)
    calc(var(--white-l) - var(--white-l) * 0.2);
  --white-d-300: var(--white-h) var(--white-s)
    calc(var(--white-l) - var(--white-l) * 0.3);
  --white-d-400: var(--white-h) var(--white-s)
    calc(var(--white-l) - var(--white-l) * 0.4);
  --white-l-100: var(--white-h) calc(var(--white-s))
    calc(var(--white-l) + (100% - var(--white-l)) * 0.1);
  --white-l-200: var(--white-h) calc(var(--white-s))
    calc(var(--white-l) + (100% - var(--white-l)) * 0.2);
  --white-l-300: var(--white-h) calc(var(--white-s))
    calc(var(--white-l) + (100% - var(--white-l)) * 0.3);
  --white-l-400: var(--white-h) calc(var(--white-s))
    calc(var(--white-l) + (100% - var(--white-l)) * 0.4);
  --light: 24 75% 11%;
  --black: 0 0% 0%;
  --border-color: 0 0% 88%;
  --section-bg: 20 100% 99%;
  --heading-color: var(--base-two);
  --body-color: var(--light) / 0.7;
  --background-color: hsl(var(--section-bg));
  --gradient-color: linear-gradient(
    180deg,
    hsl(var(--secondary)) 0%,
    hsl(var(--base)) 100%
  );
  /* ================================ Box Shadow Start =============================== */
  --header-box-shadow: 0px -1px 15px 3px hsl(var(--black) / 0.3);
  --mobile-box-shadow: 0px -1px 5px 0px hsl(var(--black) / 0.92);
  --box-shadow: 0px 2px 15px hsl(var(--black) / 0.05);
  /* ================================ Box Shadow End =============================== */
  /* ============================== Bootstrap Modifier Start ============================== */
  /* Primary Color */
  --primary-h: 211;
  --primary-s: 100%;
  --primary-l: 50%;
  --primary: var(--primary-h) var(--primary-s) var(--primary-l);
  --primary-d-100: var(--primary-h) var(--primary-s)
    calc(var(--primary-l) - var(--primary-l) * 0.1);
  --primary-d-200: var(--primary-h) var(--primary-s)
    calc(var(--primary-l) - var(--primary-l) * 0.2);
  --primary-d-300: var(--primary-h) var(--primary-s)
    calc(var(--primary-l) - var(--primary-l) * 0.3);
  --primary-d-400: var(--primary-h) var(--primary-s)
    calc(var(--primary-l) - var(--primary-l) * 0.4);
  --primary-l-100: var(--primary-h) calc(var(--primary-s))
    calc(var(--primary-l) + (100% - var(--primary-l)) * 0.1);
  --primary-l-200: var(--primary-h) calc(var(--primary-s))
    calc(var(--primary-l) + (100% - var(--primary-l)) * 0.2);
  --primary-l-300: var(--primary-h) calc(var(--primary-s))
    calc(var(--primary-l) + (100% - var(--primary-l)) * 0.3);
  --primary-l-400: var(--primary-h) calc(var(--primary-s))
    calc(var(--primary-l) + (100% - var(--primary-l)) * 0.4);
  /* Secondary Color */
  --secondary-h: 38;
  --secondary-s: 100%;
  --secondary-l: 63%;
  --secondary: var(--secondary-h) var(--secondary-s) var(--secondary-l);
  --secondary-d-100: var(--secondary-h) var(--secondary-s)
    calc(var(--secondary-l) - var(--secondary-l) * 0.1);
  --secondary-d-200: var(--secondary-h) var(--secondary-s)
    calc(var(--secondary-l) - var(--secondary-l) * 0.2);
  --secondary-d-300: var(--secondary-h) var(--secondary-s)
    calc(var(--secondary-l) - var(--secondary-l) * 0.3);
  --secondary-d-400: var(--secondary-h) var(--secondary-s)
    calc(var(--secondary-l) - var(--secondary-l) * 0.4);
  --secondary-l-100: var(--secondary-h) calc(var(--secondary-s))
    calc(var(--secondary-l) + (100% - var(--secondary-l)) * 0.1);
  --secondary-l-200: var(--secondary-h) calc(var(--secondary-s))
    calc(var(--secondary-l) + (100% - var(--secondary-l)) * 0.2);
  --secondary-l-300: var(--secondary-h) calc(var(--secondary-s))
    calc(var(--secondary-l) + (100% - var(--secondary-l)) * 0.3);
  --secondary-l-400: var(--secondary-h) calc(var(--secondary-s))
    calc(var(--secondary-l) + (100% - var(--secondary-l)) * 0.4);
  /* Success Color */
  --success-h: 144;
  --success-s: 100%;
  --success-l: 36%;
  --success: var(--success-h) var(--success-s) var(--success-l);
  --success-d-100: var(--success-h) var(--success-s)
    calc(var(--success-l) - var(--success-l) * 0.1);
  --success-d-200: var(--success-h) var(--success-s)
    calc(var(--success-l) - var(--success-l) * 0.2);
  --success-d-300: var(--success-h) var(--success-s)
    calc(var(--success-l) - var(--success-l) * 0.3);
  --success-d-400: var(--success-h) var(--success-s)
    calc(var(--success-l) - var(--success-l) * 0.4);
  --success-l-100: var(--success-h) calc(var(--success-s))
    calc(var(--success-l) + (100% - var(--success-l)) * 0.1);
  --success-l-200: var(--success-h) calc(var(--success-s))
    calc(var(--success-l) + (100% - var(--success-l)) * 0.2);
  --success-l-300: var(--success-h) calc(var(--success-s))
    calc(var(--success-l) + (100% - var(--success-l)) * 0.3);
  --success-l-400: var(--success-h) calc(var(--success-s))
    calc(var(--success-l) + (100% - var(--success-l)) * 0.4);
  /* Danger Color */
  --danger-h: 0;
  --danger-s: 96%;
  --danger-l: 45%;
  --danger: var(--danger-h) var(--danger-s) var(--danger-l);
  --danger-d-100: var(--danger-h) var(--danger-s)
    calc(var(--danger-l) - var(--danger-l) * 0.1);
  --danger-d-200: var(--danger-h) var(--danger-s)
    calc(var(--danger-l) - var(--danger-l) * 0.2);
  --danger-d-300: var(--danger-h) var(--danger-s)
    calc(var(--danger-l) - var(--danger-l) * 0.3);
  --danger-d-400: var(--danger-h) var(--danger-s)
    calc(var(--danger-l) - var(--danger-l) * 0.4);
  --danger-l-100: var(--danger-h) calc(var(--danger-s))
    calc(var(--danger-l) + (100% - var(--danger-l)) * 0.1);
  --danger-l-200: var(--danger-h) calc(var(--danger-s))
    calc(var(--danger-l) + (100% - var(--danger-l)) * 0.2);
  --danger-l-300: var(--danger-h) calc(var(--danger-s))
    calc(var(--danger-l) + (100% - var(--danger-l)) * 0.3);
  --danger-l-400: var(--danger-h) calc(var(--danger-s))
    calc(var(--danger-l) + (100% - var(--danger-l)) * 0.4);
  /* Warning Color */
  --warning-h: 40;
  --warning-s: 100%;
  --warning-l: 50%;
  --warning: var(--warning-h) var(--warning-s) var(--warning-l);
  --warning-d-100: var(--warning-h) var(--warning-s)
    calc(var(--warning-l) - var(--warning-l) * 0.1);
  --warning-d-200: var(--warning-h) var(--warning-s)
    calc(var(--warning-l) - var(--warning-l) * 0.2);
  --warning-d-300: var(--warning-h) var(--warning-s)
    calc(var(--warning-l) - var(--warning-l) * 0.3);
  --warning-d-400: var(--warning-h) var(--warning-s)
    calc(var(--warning-l) - var(--warning-l) * 0.4);
  --warning-l-100: var(--warning-h) calc(var(--warning-s))
    calc(var(--warning-l) + (100% - var(--warning-l)) * 0.1);
  --warning-l-200: var(--warning-h) calc(var(--warning-s))
    calc(var(--warning-l) + (100% - var(--warning-l)) * 0.2);
  --warning-l-300: var(--warning-h) calc(var(--warning-s))
    calc(var(--warning-l) + (100% - var(--warning-l)) * 0.3);
  --warning-l-400: var(--warning-h) calc(var(--warning-s))
    calc(var(--warning-l) + (100% - var(--warning-l)) * 0.4);
  /* Info Color */
  --info-h: 202;
  --info-s: 94%;
  --info-l: 50%;
  --info: var(--info-h) var(--info-s) var(--info-l);
  --info-d-100: var(--info-h) var(--info-s)
    calc(var(--info-l) - var(--info-l) * 0.1);
  --info-d-200: var(--info-h) var(--info-s)
    calc(var(--info-l) - var(--info-l) * 0.2);
  --info-d-300: var(--info-h) var(--info-s)
    calc(var(--info-l) - var(--info-l) * 0.3);
  --info-d-400: var(--info-h) var(--info-s)
    calc(var(--info-l) - var(--info-l) * 0.4);
  --info-l-100: var(--info-h) calc(var(--info-s))
    calc(var(--info-l) + (100% - var(--info-l)) * 0.1);
  --info-l-200: var(--info-h) calc(var(--info-s))
    calc(var(--info-l) + (100% - var(--info-l)) * 0.2);
  --info-l-300: var(--info-h) calc(var(--info-s))
    calc(var(--info-l) + (100% - var(--info-l)) * 0.3);
  --info-l-400: var(--info-h) calc(var(--info-s))
    calc(var(--info-l) + (100% - var(--info-l)) * 0.4);
  /* ============================== Bootstrap Modifier End ============================== */
}

/* ========================= Css Variables End =========================== */
/* Fully Fit image Css */
.fit-image {
  width: 100%;
  height: 100%;
  -o-object-fit: cover;
  object-fit: cover;
}
.bg-fixed {
  background-attachment: fixed !important;
}
/* ============================= Display Flex Css Start ============================= */
.flex-wrap,
.form--radio,
.form--check {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
}

.flex-align,
.action-buttons {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
}

.flex-center {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  justify-content: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
}

.flex-between {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
  -webkit-box-pack: justify;
  -ms-flex-pack: justify;
  justify-content: space-between;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
}

/* ============================= Display Flex Css End ============================= */
/* ============================= Positioning Css Class Start ===================== */
.pa-extend,
.alert__link::before {
  position: absolute;
  content: "";
}

.top-center-extend,
.custom--accordion .accordion-button[aria-expanded="true"]::after,
.custom--accordion .accordion-button[aria-expanded="false"]::after {
  top: 50%;
  -webkit-transform: translateY(-50%);
  transform: translateY(-50%);
}

.left-center-extend {
  left: 50%;
  -webkit-transform: translateX(-50%);
  transform: translateX(-50%);
}

.top-left-center-extend {
  top: 50%;
  left: 50%;
  -webkit-transform: translate(-50%, -50%);
  transform: translate(-50%, -50%);
}

/* ============================= Positioning Css Class End ===================== */
/* ===================== Font Size For responsive devices Start =================== */
.fs-10 {
  font-size: 0.625rem;
}

.fs-11 {
  font-size: 0.6875rem;
}

.fs-12 {
  font-size: 0.75rem;
}

.fs-13 {
  font-size: 0.8125rem;
}

.fs-14 {
  font-size: 0.875rem;
}

.fs-15 {
  font-size: 0.9375rem;
}

.fs-16 {
  font-size: 1rem;
}

@media screen and (max-width: 1199px) {
  .fs-16 {
    font-size: 0.9375rem;
  }
}

.fs-17 {
  font-size: 1.0625rem;
}

@media screen and (max-width: 1199px) {
  .fs-17 {
    font-size: 1rem;
  }
}

@media screen and (max-width: 767px) {
  .fs-17 {
    font-size: 0.9375rem;
  }
}

.fs-18 {
  font-size: 1.125rem;
}

@media screen and (max-width: 1399px) {
  .fs-18 {
    font-size: 1.0625rem;
  }
}

@media screen and (max-width: 767px) {
  .fs-18 {
    font-size: 1rem;
  }
}

.fs-20 {
  font-size: 1.25rem;
}

@media screen and (max-width: 1399px) {
  .fs-20 {
    font-size: 1.125rem;
  }
}

@media screen and (max-width: 767px) {
  .fs-20 {
    font-size: 1.0625rem;
  }
}

/* ===================== Font Size For responsive devices End =================== */
/* ================================= Common Typography Css Start =========================== */
* {
  margin: 0;
  padding: 0;
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
}

body {
  font-family: var(--body-font);
  color: hsl(var(--body-color));
  word-break: break-word;
  background-color: var(--background-color);
  min-height: 100vh;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
  -ms-flex-direction: column;
  flex-direction: column;
}

p {
  font-weight: 400;
  margin: 0;
  color: hsl(var(--text-color));
}

span {
  display: inline-block;
}

h1,
h2,
h3,
h4,
h5,
h6 {
  margin: 0 0 20px 0;
  font-family: var(--heading-font);
  color: hsl(var(--heading-color));
  line-height: 1.2;
  font-weight: 700;
}

@media screen and (max-width: 767px) {
  h1,
  h2,
  h3,
  h4,
  h5,
  h6 {
    margin: 0 0 16px 0;
  }
}

h1 {
  font-size: 3.75rem;
}

@media screen and (max-width: 1399px) {
  h1 {
    font-size: 3.125rem;
  }
}

@media screen and (max-width: 1199px) {
  h1 {
    font-size: 2.875rem;
  }
}

@media screen and (max-width: 991px) {
  h1 {
    font-size: 2.5rem;
  }
}

@media screen and (max-width: 767px) {
  h1 {
    font-size: 2.25rem;
  }
}

@media screen and (max-width: 575px) {
  h1 {
    font-size: 1.875rem;
  }
}

h2 {
  font-size: 2.5rem;
}

@media screen and (max-width: 1399px) {
  h2 {
    font-size: 2.25rem;
  }
}

@media screen and (max-width: 1199px) {
  h2 {
    font-size: 2rem;
  }
}

@media screen and (max-width: 991px) {
  h2 {
    font-size: 1.75rem;
  }
}

@media screen and (max-width: 767px) {
  h2 {
    font-size: 1.5rem;
  }
}

@media screen and (max-width: 575px) {
  h2 {
    font-size: 1.375rem;
  }
}

h3 {
  font-size: 2.25rem;
}

@media screen and (max-width: 1399px) {
  h3 {
    font-size: 2rem;
  }
}

@media screen and (max-width: 1199px) {
  h3 {
    font-size: 1.75rem;
  }
}

@media screen and (max-width: 991px) {
  h3 {
    font-size: 1.5rem;
  }
}

@media screen and (max-width: 767px) {
  h3 {
    font-size: 1.375rem;
  }
}

@media screen and (max-width: 575px) {
  h3 {
    font-size: 1.25rem;
  }
}

h4 {
  font-size: 2rem;
}

@media screen and (max-width: 1399px) {
  h4 {
    font-size: 1.75rem;
  }
}

@media screen and (max-width: 1199px) {
  h4 {
    font-size: 1.5rem;
  }
}

@media screen and (max-width: 991px) {
  h4 {
    font-size: 1.375rem;
  }
}

@media screen and (max-width: 767px) {
  h4 {
    font-size: 1.25rem;
  }
}

@media screen and (max-width: 575px) {
  h4 {
    font-size: 1.125rem;
  }
}

h5 {
  font-size: 1.5rem;
}

@media screen and (max-width: 1399px) {
  h5 {
    font-size: 1.25rem;
  }
}

@media screen and (max-width: 1199px) {
  h5 {
    font-size: 1.125rem;
  }
}

h6 {
  font-size: 1.125rem;
}

@media screen and (max-width: 1399px) {
  h6 {
    font-size: 1.0625rem;
  }
}

@media screen and (max-width: 1199px) {
  h6 {
    font-size: 1rem;
  }
}

h1 > a,
h2 > a,
h3 > a,
h4 > a,
h5 > a,
h6 > a {
  font-weight: inherit;
  font-size: inherit;
  color: inherit;
  -webkit-transition: 0.2s linear;
  transition: 0.2s linear;
  line-height: 1.2;
}

a {
  display: inline-block;
  -webkit-transition: 0.3s linear all;
  transition: 0.3s linear all;
  text-decoration: none;
  color: hsl(var(--info));
}

a:hover {
  color: hsl(var(--base));
}

img {
  max-width: 100%;
  height: auto;
}

select {
  cursor: pointer;
}

ul,
ol {
  padding: 0;
  margin: 0;
  list-style: none;
}

button {
  border: 0;
  background-color: transparent;
}

button:focus {
  outline: none;
  -webkit-box-shadow: none;
  box-shadow: none;
}

.form-select:focus {
  outline: 0;
  -webkit-box-shadow: none;
  box-shadow: none;
}

.dashboard-body h1,
.dashboard-body h2,
.dashboard-body h3,
.dashboard-body h4,
.dashboard-body h5,
.dashboard-body h6 {
  font-family: var(--body-font);
}

/* ================================= Common Typography Css End =========================== */
/* ================================= Custom Classes Css Start =========================== */
/* Column Extra Small Screen */
@media screen and (min-width: 425px) and (max-width: 575px) {
  .col-xsm-6 {
    width: 50%;
  }
}

/* Section Background */
.section-bg {
  background-color: hsl(var(--section-bg));
}

.text-muted {
  color: hsl(var(--white) / 0.6) !important;
}

/* Bg Image Css */
.bg-img {
  background-size: cover !important;
  background-repeat: no-repeat !important;
  background-position: center center !important;
  width: 100%;
  height: 100%;
}

/* Hide Scroll bar Css For Custom Modal */
.scroll-hide {
  position: absolute;
  overflow-y: hidden;
  padding-right: 17px;
  top: 0;
  left: 0;
  width: 100%;
}

@media screen and (max-width: 991px) {
  .scroll-hide {
    padding-right: 0;
  }
}

.scroll-hide-sm {
  position: absolute;
  overflow-y: hidden;
  top: 0;
  left: 0;
  width: calc(100% - 0px);
}

/* Overlay Start */
.body-overlay {
  position: fixed;
  width: 100%;
  height: 100%;
  content: "";
  left: 0;
  top: 0;
  background-color: hsl(var(--black) / 0.6);
  z-index: 99;
  -webkit-transition: 0.2s linear;
  transition: 0.2s linear;
  visibility: hidden;
  opacity: 0;
}

.body-overlay.show-overlay {
  visibility: visible;
  opacity: 1;
}

.gradient-text {
  background-image: -webkit-gradient(
    linear,
    left top,
    left bottom,
    from(hsl(var(--base-d-200))),
    to(hsl(var(--base)))
  );
  background-image: linear-gradient(
    180deg,
    hsl(var(--base-d-200)) 0%,
    hsl(var(--base)) 100%
  );
  -webkit-text-fill-color: transparent;
  -webkit-background-clip: text;
  background-clip: text;
}

.gradient-text::-moz-selection {
  color: hsl(var(--white));
  -webkit-text-fill-color: hsl(var(--white));
  background: transparent;
}

.gradient-text::selection {
  color: hsl(var(--white));
  -webkit-text-fill-color: hsl(var(--white));
  background: transparent;
}

.sidebar-overlay {
  position: fixed;
  width: 100%;
  height: 100%;
  content: "";
  left: 0;
  top: 0;
  background-color: hsl(var(--black) / 0.6);
  z-index: 99;
  -webkit-transition: 0.2s linear;
  transition: 0.2s linear;
  visibility: hidden;
  opacity: 0;
}

.sidebar-overlay.show {
  visibility: visible;
  opacity: 1;
  z-index: 999;
}

/* Overlay End */
/* ================================= Custom Classes Css End =========================== */
/* ====================== Section Heading ==================== */
.section-heading {
  text-align: center;
  margin-bottom: 60px;
  position: relative;
  z-index: 1;
  position: relative;
}

@media screen and (max-width: 1199px) {
  .section-heading {
    margin-bottom: 40px;
  }
}

@media screen and (max-width: 991px) {
  .section-heading {
    margin-bottom: 30px;
  }
}

@media screen and (max-width: 767px) {
  .section-heading {
    margin-bottom: 20px;
  }
}

.section-heading__title {
  margin-bottom: 0;
}

.section-heading__name {
  line-height: 1;
  padding: 10px 20px;
  background-color: hsl(var(--base) / 0.2);
  color: hsl(var(--base));
  border-radius: 8px;
  display: inline-block;
  font-weight: 600;
  margin-bottom: 16px;
}

.section-heading__name.name-white {
  background-color: hsl(var(--white));
  color: hsl(var(--base-two));
}

.section-heading__name.name-base {
  background-color: hsl(var(--base));
  color: hsl(var(--white));
}

@media screen and (max-width: 575px) {
  .section-heading__name {
    padding: 7px 16px;
    font-size: 14px;
    margin-bottom: 12px;
  }
}

.section-heading__desc {
  max-width: 670px;
  margin-left: auto;
  margin-right: auto;
  line-height: 1.8;
  font-size: clamp(1rem, 0.9431rem + 0.2427vw, 1.125rem);
}

.section-heading.style-left {
  text-align: left;
  max-width: 480px;
  width: 100%;
}

.section-heading.style-left .section-heading__title::before {
  left: 0;
  -webkit-transform: translateX(0);
  transform: translateX(0);
}

.section-heading.style-left .section-heading__desc {
  margin-left: 0;
}

@-webkit-keyframes section-name-blink {
  0% {
    opacity: 0.3;
  }

  50% {
    opacity: 0.5;
  }

  100% {
    opacity: 0.3;
  }
}

@keyframes section-name-blink {
  0% {
    opacity: 0.3;
  }

  50% {
    opacity: 0.5;
  }

  100% {
    opacity: 0.3;
  }
}

main {
  margin-top: 84px;
}

@media screen and (max-width: 991px) {
  main {
    margin-top: 75px;
  }
}

@media screen and (max-width: 767px) {
  main {
    margin-top: 66px;
  }
}

@media screen and (max-width: 575px) {
  main {
    margin-top: 56px;
  }
}

/* ====================== Section Heading En d==================== */
/* ================================= preload Css Start =========================== */
.preloader {
  position: fixed;
  z-index: 999999;
  background-color: hsl(var(--black));
  width: 100%;
  height: 100%;
}

.loader-p {
  border: 0 solid transparent;
  border-radius: 50%;
  width: 150px;
  height: 150px;
  position: absolute;
  top: calc(50vh - 75px);
  left: calc(50vw - 75px);
}

.loader-p:before,
.loader-p:after {
  content: "";
  border: 1em solid hsl(var(--base));
  border-radius: 50%;
  width: inherit;
  height: inherit;
  position: absolute;
  top: 0;
  left: 0;
  -webkit-animation: loader 2s linear infinite;
  animation: loader 2s linear infinite;
  opacity: 0;
}

.loader-p:before {
  -webkit-animation-delay: 0.5s;
  animation-delay: 0.5s;
}

@-webkit-keyframes loader {
  0% {
    -webkit-transform: scale(0);
    transform: scale(0);
    opacity: 0;
  }

  50% {
    opacity: 1;
  }

  100% {
    -webkit-transform: scale(1);
    transform: scale(1);
    opacity: 0;
  }
}

@keyframes loader {
  0% {
    -webkit-transform: scale(0);
    transform: scale(0);
    opacity: 0;
  }

  50% {
    opacity: 1;
  }

  100% {
    -webkit-transform: scale(1);
    transform: scale(1);
    opacity: 0;
  }
}

/* ================================= preload Css End ===========================  */
/* ============= Header Start Here ======================= */
.navbar-brand {
  padding-top: 0;
  padding-bottom: 0;
}

.navbar-brand.logo img {
  max-width: 165px;
}

@media screen and (max-width: 991px) {
  .navbar-brand.logo img {
    max-width: 150px;
  }
}

@media screen and (max-width: 767px) {
  .navbar-brand.logo img {
    max-width: 120px;
  }
}

.header {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  z-index: 5;
  padding: 17px 0;
}

@media screen and (max-width: 991px) {
  .header {
    padding: 15px 0;
    z-index: 999;
  }
}

@media screen and (max-width: 575px) {
  .header {
    padding: 10px 0;
  }
}

.header.fixed-header {
  background-color: hsl(var(--section-bg));
  -webkit-transition: 0.3s linear;
  transition: 0.3s linear;
  top: 0px;
  -webkit-animation: slide-down 0.8s;
  animation: slide-down 0.8s;
  width: 100%;
  -webkit-box-shadow: 0px 2px 10px hsl(var(--base) / 0.1);
  box-shadow: 0px 2px 10px hsl(var(--base) / 0.1);
}

@-webkit-keyframes slide-down {
  0% {
    opacity: 0;
    -webkit-transform: translateY(-150%);
    transform: translateY(-150%);
  }

  100% {
    opacity: 1;
    -webkit-transform: translateY(0);
    transform: translateY(0);
  }
}

@keyframes slide-down {
  0% {
    opacity: 0;
    -webkit-transform: translateY(-150%);
    transform: translateY(-150%);
  }

  100% {
    opacity: 1;
    -webkit-transform: translateY(0);
    transform: translateY(0);
  }
}

.navbar {
  padding: 0 !important;
}

/* ========================= Desktop Device Start ========================= */
@media (min-width: 992px) {
  .navbar-nav {
    position: relative;
  }

  .navbar-nav__bar {
    position: absolute;
    content: "";
    bottom: 0;
    height: 2px;
    background-color: hsl(var(--base));
    -webkit-transition: 0.3s;
    transition: 0.3s;
  }

  .nav-item {
    position: relative;
  }

  .nav-menu {
    padding-top: 0;
    padding-bottom: 0;
  }

  .nav-menu .nav-item {
    position: relative;
  }

  .nav-menu .nav-item.active .nav-link {
    color: hsl(var(--base)) !important;
  }

  /* .nav-menu .nav-item.active .active {
        background-color: hsl(var(--base-l-300)) !important;
    } */

  .nav-menu .nav-item:hover .nav-link {
    color: hsl(var(--base)) !important;
  }

  .nav-menu .nav-item:hover .nav-link::before {
    width: 100%;
  }

  .nav-menu .nav-item:hover .nav-link .nav-item__icon {
    -webkit-transform: rotate(180deg);
    transform: rotate(180deg);
    -webkit-transition: 0.2s;
    transition: 0.2s;
  }

  .nav-menu .nav-item .nav-link {
    font-weight: 500;
    font-size: 1rem;
    color: hsl(var(--base-two)) !important;
    padding: 10px 14px;
    position: relative;
    cursor: pointer;
    /* ======================== Style two ================ */
  }

  .nav-menu .nav-item .nav-link .nav-item__icon {
    -webkit-transition: 0.3s;
    transition: 0.3s;
    font-size: 0.8125rem;
    margin-left: 2px;
  }
}

@media screen and (min-width: 992px) and (max-width: 991px) {
  .nav-menu .nav-item .nav-link .nav-item__icon {
    margin-right: 6px;
  }
}

@media (min-width: 992px) {
  .dropdown-menu {
    display: block;
    visibility: hidden;
    opacity: 0;
    -webkit-transition: 0.3s;
    transition: 0.3s;
    top: 100%;
    left: 0;
    padding: 0 !important;
    -webkit-transform: scaleY(0);
    transform: scaleY(0);
    -webkit-transform-origin: top center;
    transform-origin: top center;
    transition: 0.3s;
    overflow: hidden;
    border-radius: 0;
    min-width: 190px;
  }

  .dropdown-menu__list {
    border-bottom: 1px solid hsl(var(--black) / 0.08);
  }

  .dropdown-menu__list:last-child {
    border-bottom: 3px solid hsl(var(--base));
  }

  .dropdown-menu__link {
    padding: 7px 20px;
    font-weight: 500;
    font-size: 1rem;
    -webkit-transition: 0.3s;
    transition: 0.3s;
  }

  .dropdown-menu__link:focus,
  .dropdown-menu__link:hover {
    color: hsl(var(--white));
    background-color: hsl(var(--base));
  }
}

@media (min-width: 992px) {
  .nav-menu .nav-item:hover .dropdown-menu {
    visibility: visible;
    opacity: 1;
    top: 100% !important;
    -webkit-transform: scaleY(1);
    transform: scaleY(1);
  }
}

/* ========================== Desktop Device End ========================= */
/* ============================== Small Device ======================= */
@media screen and (max-width: 991px) {
  .navbar .offcanvas-header {
    background-color: hsl(var(--base-two));
  }

  .navbar .offcanvas-header .btn-close {
    opacity: 1;
  }

  .navbar .offcanvas-body {
    background-color: hsl(var(--section-bg));
  }

  .body-overlay.show {
    visibility: visible;
    opacity: 1;
  }

  .nav-menu .nav-item {
    text-align: left;
    display: block;
    position: relative;
    margin: 0;
  }

  .nav-menu .nav-item:hover .nav-link .nav-item__icon {
    -webkit-transform: rotate(0deg) !important;
    transform: rotate(0deg) !important;
  }

  .nav-item:first-child {
    border-bottom: none;
  }

  .nav-item:last-child > a {
    border-bottom: 0;
  }

  .nav-item .nav-link {
    padding: 8px 13px !important;
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-pack: justify;
    -ms-flex-pack: justify;
    justify-content: space-between;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    border-bottom: 0;
    color: hsl(var(--base-two));
    font-size: 16px;
    font-weight: 600;
    background: hsl(var(--black) / 0.02);
    margin-bottom: 12px;
    border-radius: 6px;
  }

  .nav-item .nav-link.show[aria-expanded="true"] {
    color: hsl(var(--base)) !important;
  }

  .nav-item .nav-link.show[aria-expanded="true"] i {
    -webkit-transform: rotate(180deg);
    transform: rotate(180deg);
  }

  .dropdown-menu {
    -webkit-box-shadow: none;
    box-shadow: none;
    border-radius: 2px;
    width: 100%;
    margin: 0px !important;
    padding: 0 !important;
    border: 0;
    background-color: inherit;
    overflow: hidden;
  }

  .dropdown-menu li:nth-last-child(1) {
    border-bottom: none;
  }

  .dropdown-menu li .dropdown-item {
    padding: 10px 0px;
    font-weight: 500;
    font-size: 1rem;
    border-bottom: 1px solid hsl(var(--base-two) / 0.1);
    margin-left: 20px;
    color: hsl(var(--base-two));
  }

  .dropdown-menu li .dropdown-item:hover,
  .dropdown-menu li .dropdown-item:focus {
    background-color: transparent;
  }
}

.navbar-toggler.header-button {
  border-color: transparent;
  color: hsl(var(--base-two));
  background: transparent !important;
  padding: 0 !important;
  border: 0 !important;
  border-radius: 0 !important;
  -webkit-transition: 0.15s ease-in-out;
  transition: 0.15s ease-in-out;
  width: auto;
  font-size: 2.5rem;
}

@media screen and (max-width: 767px) {
  .navbar-toggler.header-button {
    font-size: 2rem;
  }
}

.navbar-toggler.header-button:focus {
  -webkit-box-shadow: none !important;
  box-shadow: none !important;
}

.navbar-toggler.header-button[aria-expanded="true"] i::before {
  content: "\f00d";
}

/* ================================= Header Css End =========================== */
.header-dropdown {
  border: 1px solid hsl(var(--base) / 0.15);
  border-radius: 12px;
}

@media screen and (max-width: 991px) {
  .header-dropdown {
    margin-bottom: 12px !important;
  }
}

.header-dropdown .dropdown-menu__list {
  border: 0;
}

@media screen and (max-width: 991px) {
  .header-dropdown .dropdown-menu__list .dropdown-item {
    border: 0 !important;
    padding: 7px 0;
  }
}

/* ================ Top Header Start Here ================ */
.header-top {
  background-color: hsl(var(--black));
  border-bottom: 1px solid hsl(var(--white) / 0.2);
  padding: 10px 0px;
  position: relative;
}

.header-top .social-list.style-two .social-list__link flex-center {
  width: 30px;
  height: 30px;
  font-size: 0.75rem;
}

.top-header-wrapper {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
  -webkit-box-pack: justify;
  -ms-flex-pack: justify;
  justify-content: space-between;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
}

@media screen and (max-width: 991px) {
  .top-header-wrapper {
    -webkit-box-pack: center;
    -ms-flex-pack: center;
    justify-content: center;
  }
}

.contact-list {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
}

.contact-list__item {
  padding-right: 20px;
  margin-right: 20px;
  position: relative;
}

.contact-list__item:last-child {
  padding-right: 0;
  margin-right: 0;
}

.contact-list__item:last-child::before {
  display: none;
}

.contact-list__item::before {
  position: absolute;
  content: "";
  width: 1px;
  height: 15px;
  right: 0;
  top: 50%;
  -webkit-transform: translateY(-50%);
  transform: translateY(-50%);
  background-color: hsl(var(--white) / 0.4);
}

.contact-list__item-icon {
  font-size: 0.75rem;
  width: 30px;
  height: 30px;
  line-height: 30px;
  text-align: center;
  color: hsl(var(--white));
  background-color: hsl(var(--base));
  border-radius: 50%;
  margin-right: 10px;
  display: inline-block;
}

.contact-list__link {
  color: hsl(var(--white));
  font-weight: 400;
  font-family: var(--body-font);
}

.contact-list__link:hover .contact-list__link-icon {
  color: hsl(var(--white));
}

/* Login Registration */
.login-registration-list {
  margin: 0 -10px;
  padding-right: 20px;
}

@media screen and (max-width: 991px) {
  .login-registration-list {
    padding-right: 0;
  }
}

.login-registration-list__item {
  color: hsl(var(--white));
  padding: 0 10px;
  position: relative;
}

.login-registration-list__item:last-child::before {
  display: none;
}

.login-registration-list__item::before {
  position: absolute;
  content: "";
  width: 1px;
  height: 12px;
  right: 0;
  top: 50%;
  -webkit-transform: translateY(-50%);
  transform: translateY(-50%);
  background-color: hsl(var(--white) / 0.5);
}

.login-registration-list__icon {
  color: hsl(var(--base));
  margin-right: 10px;
  font-size: 1rem;
}

.login-registration-list__link {
  color: hsl(var(--white));
  font-weight: 400;
  font-family: var(--body-font);
}

@media screen and (max-width: 575px) {
  .login-registration-list__link {
    font-size: 0.9375rem;
  }
}

.login-registration-list__link:hover {
  color: hsl(var(--base));
}

.login-registration-list__link:hover .login-registration-list__icon {
  color: hsl(var(--white));
}

@media screen and (max-width: 991px) {
  .login-registration-list__link:hover {
    color: hsl(var(--base));
  }
}

/* Language Box */
.language-box {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  position: relative;
}

.language-box .select {
  color: hsl(var(--white));
  background-color: transparent;
  border: 1px solid hsl(var(--white) / 0.4);
  padding: 5px 8px;
  font-size: 0.9375rem;
  font-weight: 400;
  border-radius: 5px;
}

.language-box .select option {
  background-color: hsl(var(--black));
}

/* ================ Top Header End Here ================ */
/* ============= Footer Start Here ======================= */
.footer-area {
  color: hsl(var(--white));
  margin-top: auto;
}

.footer-wrapper {
  position: relative;
  z-index: 1;
}

.footer-wrapper::after {
  content: "";
  position: absolute;
  height: 100%;
  width: 100%;
  background-color: hsl(var(--base-two) / 0.9);
  top: 0;
  left: 0;
  z-index: -1;
}

.footer-item__logo {
  margin-bottom: 20px;
}

.footer-item__logo a img {
  width: 100%;
  height: 100%;
  max-width: 165px;
  max-height: 50px;
}

.footer-item__desc {
  font-weight: 500;
}

.footer-item__title {
  margin-bottom: 24px;
  position: relative;
  font-weight: 800;
  background: var(--gradient-color);
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

.footer-item .social-list {
  margin-top: 30px;
}

@media screen and (max-width: 991px) {
  .footer-item .social-list {
    margin-top: 20px;
  }
}

@media screen and (max-width: 575px) {
  .footer-item .social-list {
    margin-top: 15px;
  }
}

/* Footer List Item */
.footer-menu {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
  -ms-flex-direction: column;
  flex-direction: column;
}

.footer-menu__item {
  display: block;
  padding-bottom: 12px;
}

.footer-menu__item:last-child {
  padding-bottom: 0;
}

.footer-menu__link {
  color: hsl(var(--text-color));
}

.footer-menu__link:hover {
  color: hsl(var(--base));
  text-decoration: underline;
}

/* Footer Contact */
.footer-contact-menu {
  margin-top: 24px;
}

.footer-contact-menu__item {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  padding-bottom: 12px;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  gap: 12px;
}

.footer-contact-menu__item:last-child {
  padding-bottom: 0;
}

.footer-contact-menu__item-icon {
  font-size: 1.25rem;
  -ms-flex-negative: 0;
  flex-shrink: 0;
  background: var(--gradient-color);
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

.footer-contact-menu__item-content {
  -webkit-box-flex: 1;
  -ms-flex: 1;
  flex: 1;
}

.footer-contact-menu__item-content a {
  color: hsl(var(--white));
  font-size: 16px;
  font-weight: 600;
  text-decoration-line: underline;
}

.footer-item-social {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: justify;
  -ms-flex-pack: justify;
  justify-content: space-between;
  margin-bottom: 24px;
  padding-bottom: 24px;
  border-bottom: 1px solid hsl(var(--base) / 0.2);
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
  gap: 12px;
}

.footer-item-social__title {
  position: relative;
  font-weight: 700;
  background: var(--gradient-color);
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

.footer-newsletter {
  margin-bottom: 28px;
}

.footer-newsletter__title {
  color: hsl(var(--white));
  margin-bottom: 0;
  line-height: 1.6;
}

.footer-newsletter__desc {
  line-height: 1.6;
  font-weight: 500;
}

.footer-newsletter-form {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  padding: 4px;
  border-radius: 8px;
  background-color: hsl(var(--white) / 0.05);
}

@media screen and (max-width: 767px) {
  .footer-newsletter-form {
    -webkit-box-orient: vertical;
    -webkit-box-direction: normal;
    -ms-flex-direction: column;
    flex-direction: column;
    background-color: transparent;
    -webkit-box-align: start;
    -ms-flex-align: start;
    align-items: flex-start;
    gap: 6px;
  }
}

.footer-newsletter-form .form--control {
  color: hsl(var(--white));
  border: 0;
  background-color: transparent;
}

@media screen and (max-width: 767px) {
  .footer-newsletter-form .form--control {
    background-color: hsl(var(--white) / 0.05);
  }
}

.footer-newsletter-form .form--control::-webkit-input-placeholder {
  color: hsl(var(--white) / 0.3);
}

.footer-newsletter-form .form--control::-moz-placeholder {
  color: hsl(var(--white) / 0.3);
}

.footer-newsletter-form .form--control:-ms-input-placeholder {
  color: hsl(var(--white) / 0.3);
}

.footer-newsletter-form .form--control::-ms-input-placeholder {
  color: hsl(var(--white) / 0.3);
}

.footer-newsletter-form .form--control::placeholder {
  color: hsl(var(--white) / 0.3);
}

.footer-newsletter-form .btn {
  -ms-flex-negative: 0;
  flex-shrink: 0;
}

/* ============= Footer End Here ======================= */
/* ============= Bottom Footer End Here ======================= */
.bottom-footer {
  background-color: hsl(var(--base-two));
}

/* =============Bottom Footer End Here ======================= */
/* ===================== Scroll to Top Start ================================= */
.scroll-top {
  position: fixed;
  right: 30px;
  bottom: 30px;
  color: hsl(var(--white));
  width: 48px;
  height: 48px;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  justify-content: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  border-radius: 50%;
  z-index: 5;
  -webkit-transition: 0.5s;
  transition: 0.5s;
  cursor: pointer;
  -webkit-transform: scale(0);
  transform: scale(0);
  background-color: hsl(var(--base));
}

.scroll-top:hover {
  color: hsl(var(--white));
  background-color: hsl(var(--base-d-100));
}

.scroll-top.show {
  -webkit-transform: scale(1);
  transform: scale(1);
}

/* ===================== Scroll to Top End ================================= */
/* ================================= Template Selection Css Start =========================== */
::-moz-selection {
  color: hsl(var(--white));
  background: hsl(var(--base-d-100));
}

::selection {
  color: hsl(var(--white));
  background: hsl(var(--base-d-100));
}

/* ================================= Template Selection Css End ===========================  */
/* ================================= Social Icon Css Start =========================== */
.social-list {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
}

.social-list__item {
  margin-right: 10px;
}

.social-list__item:last-child {
  margin-right: 0;
}

.social-list__link {
  width: 30px;
  height: 30px;
  border-radius: 4px;
  position: relative;
  overflow: hidden;
  -webkit-transition: 0.3s;
  transition: 0.3s;
  cursor: pointer;
  background-color: hsl(var(--base-two-l-100));
  font-size: 1.125rem;
}

.social-list__link i {
  background: var(--gradient-color);
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

.social-list__link:hover,
.social-list__link:focus {
  background-color: hsl(var(--base));
  border-color: hsl(var(--base)) !important;
}

.social-list__link:hover i,
.social-list__link:focus i {
  background: hsl(var(--white));
  -webkit-background-clip: text;
  background-clip: text;
}

@media screen and (max-width: 767px) {
  .social-list__link {
    width: 35px;
    height: 35px;
    font-size: 0.875rem;
  }
}

/* ================================= Social Icon Css End ===========================  */
/* ====================== Breadcrumb Css Start ==================== */
.breadcrumb-wrapper {
  padding: 75px 0 60px;
}

@media screen and (max-width: 991px) {
  .breadcrumb-wrapper {
    padding: 60px 0 40px;
  }
}

@media screen and (max-width: 767px) {
  .breadcrumb-wrapper {
    padding: 50px 0 30px;
  }
}

@media screen and (max-width: 575px) {
  .breadcrumb-wrapper {
    padding: 40px 0 30px;
  }
}

.breadcrumb-wrapper.style-center {
  text-align: center;
  background: hsl(var(--base) / 0.05);
}

.breadcrumb-wrapper.style-center .breadcrumb {
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  justify-content: center;
}

.breadcrumb-wrapper__title {
  margin-bottom: 12px;
  font-weight: 900;
  color: hsl(var(--base-two));
}

.breadcrumb {
  position: relative;
  margin-bottom: 0;
}

.breadcrumb-item {
  font-weight: 500;
}

.breadcrumb-item a {
  color: hsl(var(--base-two));
}

.breadcrumb-item a:hover {
  color: hsl(var(--base));
}

/* ====================== Breadcrumb Css End ==================== */
.search-form .selected-option {
  padding: 18px 0px 18px 26px;
  border: 0;
  border-radius: 0;
  cursor: pointer;
  background-color: transparent;
}

@media screen and (max-width: 767px) {
  .search-form .selected-option {
    padding: 14px 0px 14px 26px;
  }
}

@media screen and (max-width: 575px) {
  .search-form .selected-option {
    padding: 12px 0px 12px 22px;
  }
}

.search-form__wrapper {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: end;
  -ms-flex-align: end;
  align-items: flex-end;
  gap: 16px 12px;
  padding: 36px;
  border-radius: 18px;
  border: 1px solid hsl(var(--base) / 0.3);
  background: hsl(var(--white));
  -webkit-box-shadow: 0px 4px 24px 0px hsl(var(--base-two) / 0.1);
  box-shadow: 0px 4px 24px 0px hsl(var(--base-two) / 0.1);
}

@media screen and (max-width: 991px) {
  .search-form__wrapper {
    -webkit-box-orient: vertical;
    -webkit-box-direction: normal;
    -ms-flex-direction: column;
    flex-direction: column;
    padding: 24px;
  }
}

@media screen and (max-width: 575px) {
  .search-form__wrapper {
    padding: 16px;
  }
}

@media screen and (max-width: 424px) {
  .search-form__wrapper {
    gap: 12px;
  }
}

.search-form__button {
  -ms-flex-negative: 0;
  flex-shrink: 0;
}

.search-form__button .btn {
  height: 47px;
}

@media screen and (max-width: 991px) {
  .search-form__button {
    width: 100%;
    margin-top: 16px;
  }

  .search-form__button .btn {
    width: 100%;
  }
}

@media screen and (max-width: 424px) {
  .search-form__button {
    margin-top: 8px;
  }
}

.search--group {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  display: flex;
  height: 56px;
  align-items: center;
  padding: 0 24px;
  border-radius: 12px;
  border: 1px solid hsl(var(--base) / 0.3);
  background-color: hsl(var(--white));
}

@media screen and (max-width: 1199px) {
  .search--group {
    padding: 0 12px;
  }
}

@media screen and (max-width: 991px) {
  .search--group {
    height: 52px;
  }
}

@media screen and (max-width: 767px) {
  .search--group {
    height: 43px;
  }
}

.search--group-inner {
  position: relative;
  -webkit-box-flex: 1;
  -ms-flex: 1;
  flex: 1;
  height: 100%;
}

.search--group-inner.divide {
  margin-left: 32px;
  position: relative;
}

@media screen and (max-width: 1199px) {
  .search--group-inner.divide {
    margin-left: 16px;
  }
}

.search--group-inner.divide::after {
  content: "";
  position: absolute;
  height: 70%;
  background-color: hsl(var(--base) / 0.3);
  width: 1px;
  left: -16px;
  top: 50%;
  -webkit-transform: translateY(-50%);
  transform: translateY(-50%);
}

@media screen and (max-width: 1199px) {
  .search--group-inner.divide::after {
    left: -8px;
  }
}

.search--group .form--control {
  border: 0;
  padding: 0;
  height: 100%;
  color: #90859d;
  cursor: pointer;
  font-size: 14px;
  padding-left: 30px;
}

@media screen and (max-width: 1199px) {
  .search--group .form--control {
    padding-left: 20px;
  }
}

.search--group .form--control:-moz-read-only {
  background-color: transparent;
}

.search--group .form--control:read-only {
  background-color: transparent;
}

.search--group-text {
  color: #90859d;
  cursor: pointer;
  position: absolute;
  top: 50%;
  -webkit-transform: translateY(-50%);
  transform: translateY(-50%);
  left: 0;
}

.search--group-text span {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  font-size: 16px;
}

@media screen and (max-width: 1199px) {
  .search--group-text span {
    font-size: 14px;
  }
}

.search-form-item {
  width: 40%;
}

@media screen and (max-width: 991px) {
  .search-form-item {
    width: 100%;
  }
}

.search-form-item:first-child {
  width: 20%;
}

@media screen and (max-width: 991px) {
  .search-form-item:first-child {
    width: 100%;
  }
}

.search-form-item__title {
  color: hsl(var(--base-two));
  margin-bottom: 12px;
  font-weight: 500;
}

@media screen and (max-width: 575px) {
  .search-form-item__title {
    margin-bottom: 6px;
  }
}

/* ================================= Start Rating Css Start =========================== */
.rating-list {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
}

.rating-list__item {
  padding: 0 1px;
  color: hsl(var(--base));
  font-size: 20px;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
}

.rating-list__item.unselected {
  color: hsl(var(--body-color));
}

/* ================================= Start Rating Css End =========================== */
.blog-sidebar-wrapper {
  position: sticky;
  top: 100px;
}

/* ============ Sidebar search box ============= */
.search-box {
  position: relative;
}

.search-box__button {
  position: absolute;
  right: 15px;
  top: 50%;
  -webkit-transform: translateY(-50%);
  transform: translateY(-50%);
  color: hsl(var(--base));
}

/* ================== Sidebar Box & Title =================== */
.blog-sidebar {
  background-color: hsl(var(--white));
  padding: 30px 20px;
  border-radius: 12px;
  margin-bottom: 30px;
  border: 1px solid hsl(var(--base) / 0.15);
}

.blog-sidebar:last-of-type {
  margin-bottom: 0;
}

@media screen and (max-width: 575px) {
  .blog-sidebar {
    padding: 20px 15px;
  }
}

.blog-sidebar__title {
  position: relative;
  padding-bottom: 10px;
  color: hsl(var(--black));
}

.blog-sidebar__title::before {
  position: absolute;
  content: "";
  width: 60px;
  height: 2px;
  background-color: hsl(var(--base));
  left: 0;
  bottom: 0px;
}

/* ========================= Category & Tags List Style ========================= */
.text-list {
  /* Category */
  /* Style Tag */
}

.text-list.style-category .text-list__item {
  border-bottom: 1px dashed hsl(var(--base) / 0.2);
  padding: 6px 0;
}

.text-list.style-category .text-list__item:last-child {
  border-bottom: 0;
}

.text-list.style-category .text-list__link {
  color: hsl(var(--black));
}

.text-list.style-category .text-list__link:hover {
  color: hsl(var(--base));
}

.text-list.style-tag {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: horizontal;
  -webkit-box-direction: normal;
  -ms-flex-direction: row;
  flex-direction: row;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
  margin: -7px;
}

.text-list.style-tag .text-list__item {
  padding: 7px;
}

.text-list.style-tag .text-list__link {
  color: hsl(var(--black));
  border: 1px solid hsl(var(--black) / 0.08);
  padding: 6px 20px;
  border-radius: 3px;
}

.text-list.style-tag .text-list__link.active {
  background-color: hsl(var(--base));
  border-color: hsl(var(--base));
  color: hsl(var(--white));
}

.text-list.style-tag .text-list__link:hover {
  background-color: hsl(var(--base));
  border-color: hsl(var(--base));
  color: hsl(var(--white));
}

/* ======================== Latest Blog Css========================  */
.latest-blog {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
  padding-bottom: 20px;
  margin-bottom: 20px;
  border-bottom: 1px solid hsl(var(--black) / 0.1);
}

.latest-blog:last-of-type {
  padding-bottom: 0px;
  margin-bottom: 0px;
  border-bottom: 0;
}

.latest-blog__thumb {
  width: 80px;
  max-height: 80px;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  border-radius: 5px;
  overflow: hidden;
}

@media screen and (max-width: 424px) {
  .latest-blog__thumb {
    width: 60px;
  }
}

.latest-blog__thumb a {
  display: block;
  height: 100%;
  width: 100%;
}

.latest-blog__content {
  width: calc(100% - 80px);
  padding-left: 15px;
}

@media screen and (max-width: 424px) {
  .latest-blog__content {
    width: calc(100% - 60px);
  }
}

.latest-blog__title {
  margin-bottom: 5px;
}

.latest-blog__title a {
  color: hsl(var(--heading-color));
  font-weight: 500;
  font-size: 1rem;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
}

.latest-blog__title a:hover {
  color: hsl(var(--base));
}

/* ========================= Latest Blog Css End ===================*/
/* ================================= Dashboard Fulid Sidebar Css Start =========================== */
.dashboard .sidebar-menu {
  background-color: hsl(var(--white));
  width: 300px;
  padding: 32px 24px;
  border-radius: 16px;
  border: 1px solid hsl(var(--base) / 0.2);
  position: sticky;
  top: 100px;
}

.dashboard .sidebar-menu::-webkit-scrollbar {
  width: 3px;
  height: 3px;
}

.dashboard .sidebar-menu::-webkit-scrollbar-thumb {
  background-color: hsl(var(--black) / 0.15);
}

.dashboard .sidebar-menu.show-sidebar {
  -webkit-transform: translateX(0);
  transform: translateX(0);
}

@media screen and (max-width: 991px) {
  .dashboard .sidebar-menu {
    -webkit-transform: translateX(-100%);
    transform: translateX(-100%);
    z-index: 9992;
    border-radius: 0;
    height: 100vh;
    overflow-y: auto;
    z-index: 999;
    -webkit-transition: 0.2s linear;
    transition: 0.2s linear;
    width: 300px;
    border: 0;
    border-right: 1px solid hsl(var(--base-two) / 0.15);
    position: fixed;
    left: 0;
    top: 0;
    -webkit-box-align: start;
    -ms-flex-align: start;
    align-items: start;
    -webkit-box-orient: horizontal;
    -webkit-box-direction: normal;
    -ms-flex-direction: row;
    flex-direction: row;
  }
}

.dashboard .sidebar-menu__close {
  position: absolute;
  top: 8px;
  right: 16px;
  color: hsl(var(--text-color));
  font-size: 1.25rem;
  -webkit-transition: 0.2s linear;
  transition: 0.2s linear;
  cursor: pointer;
  z-index: 9;
}

.dashboard .sidebar-menu__close:active {
  top: 14px;
}

.dashboard .sidebar-menu__close:hover,
.dashboard .sidebar-menu__close:focus {
  background-color: hsl(var(--white));
  border-color: hsl(var(--white));
  color: hsl(var(--base));
}

.dashboard .sidebar-menu-list__item {
  margin-bottom: 6px;
}

.dashboard .sidebar-menu-list__item:last-child .sidebar-menu-list__link {
  border-bottom: 0;
}

.dashboard .sidebar-menu-list__item.active > a {
  background: var(--gradient-color);
  color: hsl(var(--base-two));
}

.dashboard .sidebar-menu-list__item.active > a .icon {
  -webkit-text-fill-color: hsl(var(--base-two));
}

.dashboard .sidebar-menu-list__link {
  display: inline-block;
  text-decoration: none;
  position: relative;
  padding: 8px 16px;
  width: 100%;
  color: hsl(var(--base-two));
  font-weight: 600;
  border-radius: 8px;
  line-height: 1.5;
}

.dashboard .sidebar-menu-list__link:hover {
  background-color: hsl(var(--base-two) / 0.06);
}

.dashboard .sidebar-menu-list__link .icon {
  margin-right: 8px;
  font-size: 16px;
  text-align: center;
  border-radius: 4px;
  background: var(--gradient-color);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

.dashboard .auth-user {
  margin-bottom: 24px;
  text-align: center;
}

.dashboard .auth-user__image {
  width: 120px;
  height: 120px;
  -ms-flex-negative: 0;
  flex-shrink: 0;
  padding: 7px;
  border: 2px solid hsl(var(--base));
  border-radius: 50%;
  margin: 0 auto 8px;
}

.dashboard .auth-user__image img {
  height: 100%;
  width: 100%;
  -o-object-fit: cover;
  object-fit: cover;
  border-radius: 50%;
}

.dashboard .auth-user__name {
  line-height: 1.3;
  font-weight: 700;
  margin-bottom: 0;
}

.dashboard .auth-user__email {
  color: hsl(var(--black));
  line-height: 150%;
}

/* ================================= Dashboard Css Start =========================== */
.dashboard {
  position: relative;
}

.dashboard__inner {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: start;
  -ms-flex-align: start;
  align-items: flex-start;
  gap: 24px;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
}

.dashboard__right {
  -webkit-box-flex: 1;
  -ms-flex: 1;
  flex: 1;
}

.dashboard .dashboard-body {
  position: relative;
}

.dashboard .dashboard-body__bar-icon {
  color: hsl(var(--text-color));
  font-size: 1.5625rem;
  margin-bottom: 10px;
  cursor: pointer;
}

.dashboard .dashboard-widget {
  padding: 12px 20px;
  border-radius: 16px;
  position: relative;
  z-index: 1;
  overflow: hidden;
  background-color: hsl(var(--white));
  -webkit-transition: 0.25s ease-in-out;
  transition: 0.25s ease-in-out;
  border: 1px solid hsl(var(--base) / 0.2);
  z-index: 1;
}

@media screen and (max-width: 1399px) {
  .dashboard .dashboard-widget {
    padding: 24px;
  }
}

.dashboard .dashboard-widget__shape {
  position: absolute;
  top: 0;
  right: 0;
  z-index: -1;
  opacity: 0.5;
}

.dashboard .dashboard-widget__shape img {
  -webkit-mask-image: url(../images/thumbs/shape.png);
  -webkit-mask-position: center;
  -webkit-mask-size: cover;
  -webkit-mask-repeat: no-repeat;
  -o-object-fit: cover;
  object-fit: cover;
  background-color: hsl(var(--base));
}

.dashboard .dashboard-widget__icon {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  background: var(--gradient-color);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  font-size: 42px;
  margin-bottom: 20px;
}

.dashboard .dashboard-widget__number {
  margin-bottom: 4px;
}

.dashboard .dashboard-widget__text {
  color: hsl(var(--body-color));
  font-weight: 500;
  line-height: 150%;
}

.profile-setting .custom--tab {
  border: 0;
  gap: 50px;
  border-radius: 0;
  margin-bottom: 0;
}

@media screen and (max-width: 991px) {
  .profile-setting .custom--tab {
    gap: 24px;
  }
}

.profile-setting .custom--tab .nav-link {
  font-size: 24px !important;
  font-weight: 700 !important;
  padding: 0 !important;
  line-height: 1.7 !important;
}

@media screen and (max-width: 1199px) {
  .profile-setting .custom--tab .nav-link {
    padding: 0 !important;
  }
}

@media screen and (max-width: 991px) {
  .profile-setting .custom--tab .nav-link {
    font-size: 20px !important;
    padding: 0 !important;
  }
}

.profile-setting .form--control,
.profile-setting .selected-option {
  border: 1px solid hsl(var(--base-two) / 0.15);
  background: hsl(var(--section-bg));
}

.permition-inner {
  padding: 12px 24px;
  border-radius: 8px;
  background-color: hsl(var(--section-bg));
  border: 1px solid hsl(var(--base-two) / 0.15);
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: justify;
  -ms-flex-pack: justify;
  justify-content: space-between;
  gap: 32px;
  height: 100%;
}

@media screen and (max-width: 1399px) {
  .permition-inner {
    gap: 16px;
    padding: 12px 16px;
  }
}

.permition-inner__content {
  -webkit-box-flex: 1;
  -ms-flex: 1;
  flex: 1;
}

.permition-inner__title {
  font-weight: 700;
  color: hsl(var(--base-two));
  margin-bottom: 8px;
  line-height: 1.5;
}

.permition-inner__desc {
  color: hsl(var(--base-two));
  font-size: 14px;
}

.staff_permision_input {
  position: relative;
  -ms-flex-negative: 0;
  flex-shrink: 0;
}

.label_check {
  width: 54px;
  height: 28px;
  position: relative;
  display: block;
  cursor: pointer;
}

.label_check input {
  -webkit-appearance: none;
  -moz-appearance: none;
  appearance: none;
}

.label_checkBg {
  position: absolute;
  top: 0px;
  left: 0px;
  background: hsl(var(--base-two) / 0.4);
  height: 100%;
  width: 100%;
  border-radius: 20px;
  -webkit-transition: all linear 0.3s;
  transition: all linear 0.3s;
}

.label_check input:checked ~ .label_checkBg {
  background: hsl(var(--success));
}

.label_checkround {
  position: absolute;
  height: 22px;
  width: 22px;
  border-radius: 50%;
  background-color: hsl(var(--white));
  left: 5px;
  top: 50%;
  -webkit-transform: translateY(-50%);
  transform: translateY(-50%);
  -webkit-transition: all linear 0.3s;
  transition: all linear 0.3s;
}

.label_check input:checked ~ .label_checkround {
  left: calc(100% - 27px);
  background-color: hsl(var(--white));
}

.dashboard-body__bar {
  -webkit-box-pack: justify;
  -ms-flex-pack: justify;
  justify-content: space-between;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  padding: 8px 16px;
  border: 1px solid hsl(var(--base) / 0.2);
  border-radius: 12px;
  margin-bottom: 24px;
  background-color: hsl(var(--white));
}

.dashboard-body__bar-icon {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  margin-bottom: 0 !important;
}

.dashboard-profile {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  gap: 6px;
}

.dashboard-profile img {
  height: 36px;
  width: 36px;
  padding: 4px;
  border: 1px solid hsl(var(--base) / 0.4);
  border-radius: 50%;
}

.dashboard-profile .name {
  font-weight: 700;
  font-size: 14px;
}

/* ================= Slick Arrow & Dots css Start ================ */
.slick-initialized.slick-slider {
  margin: 0 -10px;
}

.slick-initialized.slick-slider .slick-track {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
}

.slick-initialized.slick-slider .slick-slide {
  height: auto;
  padding: 0 10px;
}

.slick-initialized.slick-slider .slick-slide > div {
  height: 100%;
}

.slick-arrow {
  position: absolute;
  z-index: 1;
  top: 50%;
  -webkit-transform: translateY(-50%);
  transform: translateY(-50%);
  border: none;
  color: hsl(var(--white));
  width: 56px;
  height: 56px;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  justify-content: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  border-radius: 50%;
  -webkit-transition: 0.2s linear;
  transition: 0.2s linear;
  background-color: var(--background-color);
  color: hsl(var(--body-color));
  border: 1px solid hsl(var(--secondary));
  font-size: 20px;
}

@media screen and (max-width: 1499px) {
  .slick-arrow {
    width: 48px;
    height: 48px;
    font-size: 16px;
  }
}

@media screen and (max-width: 575px) {
  .slick-arrow {
    width: 44px;
    height: 44px;
  }
}

.slick-arrow:hover {
  border-color: hsl(var(--base));
  background-color: hsl(var(--base));
  color: hsl(var(--white));
}

.slick-next {
  right: -20px;
}

@media screen and (max-width: 991px) {
  .slick-next {
    right: -10px;
  }
}

@media screen and (max-width: 575px) {
  .slick-next {
    right: 10px;
  }
}

.slick-prev {
  left: -20px;
}

@media screen and (max-width: 991px) {
  .slick-prev {
    left: -10px;
  }
}

@media screen and (max-width: 575px) {
  .slick-prev {
    left: 10px;
  }
}

.slick-dots {
  text-align: center;
  padding-top: 20px;
}

@media screen and (max-width: 767px) {
  .slick-dots {
    padding-top: 16px;
  }
}

.slick-dots li {
  display: inline-block;
}

.slick-dots li button {
  border: none;
  background-color: hsl(var(--black) / 0.2);
  color: hsl(var(--white));
  margin: 0 3px;
  width: 8px;
  height: 8px;
  border-radius: 1px;
  border-radius: 50%;
  text-indent: -9999px;
  -webkit-transition: 0.3s linear;
  transition: 0.3s linear;
}

.slick-dots li.slick-active button {
  background-color: hsl(var(--base));
  width: 25px;
  border-radius: 5px;
}

/* ================================= Background Color Css Start =========================== */
.bg--base {
  background-color: hsl(var(--base)) !important;
}

.bg--primary {
  background-color: hsl(var(--primary)) !important;
}

.bg--secondary {
  background-color: hsl(var(--secondary)) !important;
}

.bg--success {
  background-color: hsl(var(--success)) !important;
}

.bg--danger {
  background-color: hsl(var(--danger)) !important;
}

.bg--warning {
  background-color: hsl(var(--warning)) !important;
}

.bg--info {
  background-color: hsl(var(--info)) !important;
}

/* ================================= Background Color Css End =========================== */
/* ================================= Color Css Start =========================== */
.text--base {
  color: hsl(var(--base)) !important;
}

.text--primary {
  color: hsl(var(--primary)) !important;
}

.text--secondary {
  color: hsl(var(--secondary)) !important;
}

.text--success {
  color: hsl(var(--success)) !important;
}

.text--danger {
  color: hsl(var(--danger)) !important;
}

.text--warning {
  color: hsl(var(--warning)) !important;
}

.text--info {
  color: hsl(var(--info)) !important;
}

/* ================================= Color Css End =========================== */
/* ================================= margin Css Start =========================== */
.my-120 {
  margin-top: 30px;
  margin-bottom: 30px;
}

@media (min-width: 576px) {
  .my-120 {
    margin-top: 60px;
    margin-bottom: 60px;
  }
}

@media (min-width: 992px) {
  .my-120 {
    margin-top: 120px;
    margin-bottom: 120px;
  }
}

.mt-120 {
  margin-top: 30px;
}

@media (min-width: 576px) {
  .mt-120 {
    margin-top: 60px;
  }
}

@media (min-width: 992px) {
  .mt-120 {
    margin-top: 120px;
  }
}

.mb-120 {
  margin-bottom: 30px;
}

@media (min-width: 576px) {
  .mb-120 {
    margin-bottom: 60px;
  }
}

@media (min-width: 992px) {
  .mb-120 {
    margin-bottom: 120px;
  }
}

.my-60 {
  margin-top: 20px;
  margin-bottom: 20px;
}

@media (min-width: 576px) {
  .my-60 {
    margin-top: 30px;
    margin-bottom: 30px;
  }
}

@media (min-width: 992px) {
  .my-60 {
    margin-top: 60px;
    margin-bottom: 60px;
  }
}

.mt-60 {
  margin-top: 20px;
}

@media (min-width: 576px) {
  .mt-60 {
    margin-top: 30px;
  }
}

@media (min-width: 992px) {
  .mt-60 {
    margin-top: 60px;
  }
}

.mb-60 {
  margin-bottom: 20px;
}

@media (min-width: 576px) {
  .mb-60 {
    margin-bottom: 30px;
  }
}

@media (min-width: 992px) {
  .mb-60 {
    margin-bottom: 60px;
  }
}

/* ================================= margin Css End =========================== */
/* ================================= padding Css Start =========================== */
.py-120 {
  padding-top: 40px;
  padding-bottom: 40px;
}

@media (min-width: 576px) {
  .py-120 {
    padding-top: 60px;
    padding-bottom: 60px;
  }
}

@media (min-width: 992px) {
  .py-120 {
    padding-top: 120px;
    padding-bottom: 120px;
  }
}

.pt-120 {
  padding-top: 40px;
}

@media (min-width: 576px) {
  .pt-120 {
    padding-top: 60px;
  }
}

@media (min-width: 992px) {
  .pt-120 {
    padding-top: 120px;
  }
}

.pb-120 {
  padding-bottom: 40px;
}

@media (min-width: 576px) {
  .pb-120 {
    padding-bottom: 60px;
  }
}

@media (min-width: 992px) {
  .pb-120 {
    padding-bottom: 120px;
  }
}

.py-60 {
  padding-top: 20px;
  padding-bottom: 20px;
}

@media (min-width: 576px) {
  .py-60 {
    padding-top: 30px;
    padding-bottom: 30px;
  }
}

@media (min-width: 992px) {
  .py-60 {
    padding-top: 60px;
    padding-bottom: 60px;
  }
}

.pt-60 {
  padding-top: 20px;
}

@media (min-width: 576px) {
  .pt-60 {
    padding-top: 30px;
  }
}

@media (min-width: 992px) {
  .pt-60 {
    padding-top: 60px;
  }
}

.pb-60 {
  padding-bottom: 20px;
}

@media (min-width: 576px) {
  .pb-60 {
    padding-bottom: 30px;
  }
}

@media (min-width: 992px) {
  .pb-60 {
    padding-bottom: 60px;
  }
}

/* ================================= padding Css End =========================== */
/* ================================= Border Color Css Start =========================== */
.border--base {
  border-color: hsl(var(--base)) !important;
}

.border--primary {
  border-color: hsl(var(--primary)) !important;
}

.border--secondary {
  border-color: hsl(var(--secondary)) !important;
}

.border--success {
  border-color: hsl(var(--success)) !important;
}

.border--danger {
  border-color: hsl(var(--danger)) !important;
}

.border--warning {
  border-color: hsl(var(--warning)) !important;
}

.border--info {
  border-color: hsl(var(--info)) !important;
}

/* ================================= Border Color Css End =========================== */
/* =========================== Accordion Css start ============================= */
.custom--accordion .accordion-item {
  border: 1px solid hsl(var(--base-two) / 0.1);
  background-color: hsl(var(--white)) !important;
  border-radius: 8px;
  overflow: hidden;
}

.custom--accordion .accordion-item:not(:last-child) {
  margin-bottom: 16px;
}

.custom--accordion .accordion-header {
  line-height: 1;
}

.custom--accordion .accordion-body {
  padding: 26px;
  padding-top: 0;
}

.custom--accordion .accordion-body .text {
  max-width: 60%;
}

@media screen and (max-width: 575px) {
  .custom--accordion .accordion-body .text {
    max-width: 100%;
  }
}

.custom--accordion:first-of-type .accordion-button.collapsed {
  border-radius: 8px;
}

.custom--accordion:last-of-type .accordion-button.collapsed {
  border-radius: 8px;
}

.custom--accordion .accordion-button {
  font-size: 16px;
  padding: 16px 24px;
  font-weight: 700;
  color: hsl(var(--base-two) / 0.6);
  line-height: 1.5;
}

@media screen and (max-width: 575px) {
  .custom--accordion .accordion-button {
    padding: 16px 20px;
    padding-right: 30px;
  }
}

.custom--accordion .accordion-button::after {
  background-image: none;
}

.custom--accordion .accordion-button:focus {
  -webkit-box-shadow: none;
  box-shadow: none;
}

.custom--accordion .accordion-button:not(.collapsed) {
  background-color: hsl(var(--white)) !important;
  -webkit-box-shadow: none;
  box-shadow: none;
  color: hsl(var(--base-two));
}

.custom--accordion .accordion-button:not(.collapsed)::after {
  background-image: none;
  color: hsl(var(--base-two) / 0.6);
}

.custom--accordion .accordion-button[aria-expanded="true"]::after,
.custom--accordion .accordion-button[aria-expanded="false"]::after {
  font-family: "Font Awesome 5 Free";
  font-weight: 900;
  content: "\f077";
  display: inline-block;
  position: absolute;
  right: 15px;
  height: unset;
  color: hsl(var(--base-two));
}

@media screen and (max-width: 575px) {
  .custom--accordion .accordion-button[aria-expanded="true"]::after,
  .custom--accordion .accordion-button[aria-expanded="false"]::after {
    right: 10px;
  }
}

.custom--accordion .accordion-button[aria-expanded="false"]::after {
  content: "\f078";
  color: hsl(var(--text-color));
}

/* ================================= Accordion Css End =========================== */
/* ================================= Button Css Start =========================== */
.btn-check:checked + .btn,
.btn.active,
.btn.show,
.btn:first-child:active,
:not(.btn-check) + .btn:active {
  color: none;
  background-color: none;
  border-color: none;
}

.pill {
  border-radius: 40px !important;
}

.btn {
  color: hsl(var(--base-two)) !important;
  font-weight: 600;
  padding: 10px 20px;
  border-radius: 8px;
  position: relative;
  z-index: 1;
  border: 1px solid transparent;
  font-family: var(--body-font);
  display: inline-block !important;
  font-size: 0.875rem;
  line-height: 1.5;
}

.btn:hover,
.btn:focus,
.btn:focus-visible {
  -webkit-box-shadow: none !important;
  box-shadow: none !important;
}

.btn:active {
  top: 1px;
}

.btn--lg {
  padding: 15px 30px;
  font-size: 16px;
}

@media screen and (max-width: 991px) {
  .btn--lg {
    padding: 13px 24px;
  }
}

@media screen and (max-width: 767px) {
  .btn--lg {
    padding: 10px 20px;
    font-size: 14px;
  }
}

.btn--sm {
  padding: 7px 18px;
  font-weight: 600;
  font-size: 0.75rem;
}

@media screen and (max-width: 767px) {
  .btn--sm {
    padding: 8px 12px;
  }
}

.btn .icon {
  margin-left: 4px;
}

.btn--gradient {
  position: relative;
  -webkit-box-shadow: 0px 6px 22px 0px hsl(var(--base) / 0.5);
  box-shadow: 0px 6px 22px 0px hsl(var(--base) / 0.5);
}

.btn--gradient::before,
.btn--gradient::after {
  position: absolute;
  content: "";
  top: 50%;
  left: 50%;
  -webkit-transform: translate(-50%, -50%);
  transform: translate(-50%, -50%);
  width: calc(100% + 2px);
  height: calc(100% + 2px);
  background: -webkit-gradient(
    linear,
    left top,
    right top,
    from(hsl(var(--secondary))),
    to(hsl(var(--base)))
  );
  background: linear-gradient(
    90deg,
    hsl(var(--secondary)) 0%,
    hsl(var(--base)) 100%
  );
  border-radius: inherit !important;
  z-index: -1;
  -webkit-transition: 0.2s linear;
  transition: 0.2s linear;
}

.btn--gradient::after {
  background: -webkit-gradient(
    linear,
    left top,
    right top,
    from(hsl(var(--base))),
    to(hsl(var(--secondary)))
  );
  background: linear-gradient(
    90deg,
    hsl(var(--base)) 0%,
    hsl(var(--secondary)) 100%
  );
  visibility: hidden;
  opacity: 0;
}

.btn--gradient:hover::after,
.btn--gradient:focus .btn--gradient:focus-visible::after {
  visibility: visible;
  opacity: 1;
}

.btn-outline--gradient {
  background-color: transparent !important;
  border: 1px solid hsl(var(--base)) !important;
  color: hsl(var(--base)) !important;
  position: relative;
  -webkit-transition: 0.2s linear;
  transition: 0.2s linear;
}

.btn-outline--gradient::before,
.btn-outline--gradient::after {
  position: absolute;
  content: "";
  top: 50%;
  left: 50%;
  -webkit-transform: translate(-50%, -50%);
  transform: translate(-50%, -50%);
  width: calc(100% + 2px);
  height: calc(100% + 2px);
  background: -webkit-gradient(
    linear,
    left top,
    right top,
    from(hsl(var(--secondary))),
    to(hsl(var(--base)))
  );
  background: linear-gradient(
    90deg,
    hsl(var(--secondary)) 0%,
    hsl(var(--base)) 100%
  );
  border-radius: inherit !important;
  z-index: -1;
  -webkit-transition: 0.2s linear;
  transition: 0.2s linear;
  visibility: hidden;
  opacity: 0;
}

.btn-outline--gradient:hover,
.btn-outline--gradient:focus .btn-outline--gradient:focus-visible {
  color: hsl(var(--white)) !important;
}

.btn-outline--gradient:hover::before,
.btn-outline--gradient:hover::after,
.btn-outline--gradient:focus .btn-outline--gradient:focus-visible::before,
.btn-outline--gradient:focus .btn-outline--gradient:focus-visible::after {
  visibility: visible;
  opacity: 1;
}

.btn--base {
  background-color: hsl(var(--base)) !important;
}

.btn--base:hover,
.btn--base:focus .btn--base:focus-visible {
  background-color: hsl(var(--base-d-200)) !important;
  border: 1px solid hsl(var(--base-d-200)) !important;
}

.btn-outline--base {
  background-color: transparent !important;
  border: 1px solid hsl(var(--base)) !important;
  color: hsl(var(--base)) !important;
}

.active.btn-outline--base {
  background-color: hsl(var(--base)) !important;
  color: hsl(var(--white)) !important;
}

.btn-outline--base:hover,
.btn-outline--base:focus .btn-outline--base:focus-visible {
  background-color: hsl(var(--base)) !important;
  color: hsl(var(--white)) !important;
}

.btn--base-two {
  background-color: hsl(var(--base-two)) !important;
}

.btn--base-two:hover,
.btn--base-two:focus .btn--base-two:focus-visible {
  background-color: hsl(var(--base-two-d-200)) !important;
  border: 1px solid hsl(var(--base-two-d-200)) !important;
}

.btn-outline--base-two {
  background-color: transparent !important;
  border: 1px solid hsl(var(--base-two)) !important;
  color: hsl(var(--base-two)) !important;
}

.btn-outline--base-two:hover,
.btn-outline--base-two:focus .btn-outline--base-two:focus-visible {
  background-color: hsl(var(--base-two)) !important;
  color: hsl(var(--white)) !important;
}

.btn--primary {
  background-color: hsl(var(--primary)) !important;
}

.btn--primary:hover,
.btn--primary:focus .btn--primary:focus-visible {
  background-color: hsl(var(--primary-d-200)) !important;
  border: 1px solid hsl(var(--primary-d-200)) !important;
}

.btn-outline--primary {
  background-color: transparent !important;
  border: 1px solid hsl(var(--primary)) !important;
  color: hsl(var(--primary)) !important;
}

.btn-outline--primary:hover,
.btn-outline--primary:focus .btn-outline--primary:focus-visible {
  background-color: hsl(var(--primary)) !important;
  color: hsl(var(--white)) !important;
}

.btn--secondary {
  background-color: hsl(var(--secondary)) !important;
}

.btn--secondary:hover,
.btn--secondary:focus .btn--secondary:focus-visible {
  background-color: hsl(var(--secondary-d-200)) !important;
  border: 1px solid hsl(var(--secondary-d-200)) !important;
}

.btn-outline--secondary {
  background-color: transparent !important;
  border: 1px solid hsl(var(--secondary)) !important;
  color: hsl(var(--secondary)) !important;
}

.btn-outline--secondary:hover,
.btn-outline--secondary:focus .btn-outline--secondary:focus-visible {
  background-color: hsl(var(--secondary)) !important;
  color: hsl(var(--white)) !important;
}

.btn--danger {
  background-color: hsl(var(--danger)) !important;
}

.btn--danger:hover,
.btn--danger:focus .btn--danger:focus-visible {
  background-color: hsl(var(--danger-d-200)) !important;
  border: 1px solid hsl(var(--danger-d-200)) !important;
}

.btn-outline--danger {
  background-color: transparent !important;
  border: 1px solid hsl(var(--danger)) !important;
  color: hsl(var(--danger)) !important;
}

.active.btn-outline--danger {
  background-color: hsl(var(--danger)) !important;
  color: hsl(var(--white)) !important;
}

.btn-outline--danger:hover,
.btn-outline--danger:focus .btn-outline--danger:focus-visible {
  background-color: hsl(var(--danger)) !important;
  color: hsl(var(--white)) !important;
}

.btn--warning {
  background-color: hsl(var(--warning)) !important;
}

.btn--warning:hover,
.btn--warning:focus .btn--warning:focus-visible {
  background-color: hsl(var(--warning-d-200)) !important;
  border: 1px solid hsl(var(--warning-d-200)) !important;
}

.btn-outline--warning {
  background-color: transparent !important;
  border: 1px solid hsl(var(--warning)) !important;
  color: hsl(var(--warning)) !important;
}

.active.btn-outline--warning {
  background-color: hsl(var(--warning)) !important;
  color: hsl(var(--white)) !important;
}

.btn-outline--warning:hover,
.btn-outline--warning:focus .btn-outline--warning:focus-visible {
  background-color: hsl(var(--warning)) !important;
  color: hsl(var(--white)) !important;
}

.btn--info {
  background-color: hsl(var(--info)) !important;
}

.btn--info:hover,
.btn--info:focus .btn--info:focus-visible {
  background-color: hsl(var(--info-d-200)) !important;
  border: 1px solid hsl(var(--info-d-200)) !important;
}

.btn--success {
  background-color: hsl(var(--success)) !important;
}

.btn--success:hover,
.btn--success:focus .btn--info:focus-visible {
  background-color: hsl(var(--success-d-200)) !important;
  border: 1px solid hsl(var(--success-d-200)) !important;
}

.btn-outline--success {
  background-color: transparent !important;
  border: 1px solid hsl(var(--success)) !important;
  color: hsl(var(--success)) !important;
}

.active.btn-outline--success {
  background-color: hsl(var(--success)) !important;
  color: hsl(var(--white)) !important;
}

.btn-outline--success:hover,
.btn-outline--success:focus .btn-outline--success:focus-visible {
  background-color: hsl(var(--success)) !important;
  color: hsl(var(--white)) !important;
}

.btn-outline--info {
  background-color: transparent !important;
  border: 1px solid hsl(var(--info)) !important;
  color: hsl(var(--info)) !important;
}

.btn-outline--info:hover,
.btn-outline--info:focus .btn-outline--info:focus-visible {
  background-color: hsl(var(--info)) !important;
  color: hsl(var(--white)) !important;
}

.btn--white {
  background-color: hsl(var(--white)) !important;
}

.btn--white:hover,
.btn--white:focus .btn--white:focus-visible {
  background-color: hsl(var(--white-d-200)) !important;
  border: 1px solid hsl(var(--white-d-200)) !important;
}

.btn--black {
  background-color: hsl(var(--black)) !important;
  color: hsl(var(--white)) !important;
}

.btn--black:hover,
.btn--black:focus .btn--black:focus-visible {
  background-color: hsl(var(--black) / 0.8) !important;
  border: 1px solid hsl(var(--black) / 0.8) !important;
}

.btn-outline--white {
  background-color: transparent !important;
  border: 1px solid hsl(var(--white)) !important;
  color: hsl(var(--white)) !important;
}

.btn-outline--white:hover,
.btn-outline--white:focus .btn-outline--white:focus-visible {
  background-color: hsl(var(--white)) !important;
  color: hsl(var(--white)) !important;
}

/* ================================= Button Css End =========================== */
/* ================================= Card Css Start =========================== */
.custom--card {
  border-radius: 16px;
  background-color: hsl(var(--white));
  border: 1px solid hsl(var(--base) / 0.2);
}

.custom--card:not(:has(.card-header)) .card-body {
  border-radius: 16px 16px 0 0 !important;
}

.custom--card:not(:has(.card-header, .card-footer)) .card-body {
  border-radius: 16px !important;
}

.custom--card .card-header {
  padding: 26px 24px 0;
  background-color: transparent;
  border-bottom: 0;
}

.custom--card .card-header .title {
  margin-bottom: 0;
}

.custom--card .card-body {
  background-color: hsl(var(--white));
  padding: 26px 24px;
  border-radius: 5px;
}

.custom--card .card-body:not(:has(.card-footer)) {
  border-radius: 0 0 16px 16px;
}

.custom--card .card-footer {
  padding: 0px 24px 26px;
  background-color: transparent;
  border-top: 0;
}

/* ================================= Card Css End =========================== */
/* ================================= Form Css Start =========================== */
/* Form Label */
.form--label {
  margin-bottom: 6px;
  font-size: 0.9375rem;
  color: hsl(var(--black) / 0.6);
  font-weight: 500;
}

.form-group {
  margin-bottom: 1rem;
}

.filter-btn {
  height: 47px;
}

/* Form Select */
.select {
  color: hsl(var(--black) / 0.6) !important;
}

.select:focus {
  border-color: hsl(var(--base));
  color: hsl(var(--black)) !important;
}

.select option {
  background-color: hsl(var(--black));
  color: hsl(var(--white));
}

/* Form Select End */
/* Form Control Start */
.form--control {
  border-radius: 8px;
  font-weight: 400;
  outline: none;
  width: 100%;
  padding: 13px 24px;
  background-color: hsl(var(--white));
  border: 1px solid hsl(var(--base-two) / 0.1);
  color: hsl(var(--base-two));
  line-height: 1;
}

.form--control::-webkit-input-placeholder {
  color: hsl(var(--base-two) / 0.4);
  font-size: 1rem;
}

.form--control::-moz-placeholder {
  color: hsl(var(--base-two) / 0.4);
  font-size: 1rem;
}

.form--control:-ms-input-placeholder {
  color: hsl(var(--base-two) / 0.4);
  font-size: 1rem;
}

.form--control::-ms-input-placeholder {
  color: hsl(var(--base-two) / 0.4);
  font-size: 1rem;
}

.form--control::placeholder {
  color: hsl(var(--base-two) / 0.4);
  font-size: 1rem;
}

.form--control:focus {
  border-color: hsl(var(--base));
  box-shadow: unset !important;
}

.form--control:disabled,
.form--control[readonly] {
  background-color: hsl(var(--black) / 0.2);
  opacity: 1;
  border: 0;
}

.form--control[type="password"] {
  color: hsl(var(--black) / 0.5);
}

.form--control[type="password"]:focus {
  color: hsl(var(--black));
}

.form--control[type="file"] {
  line-height: 50px;
  padding: 0;
  position: relative;
}

.form--control[type="file"]::-webkit-file-upload-button {
  border: 1px solid hsl(var(--black) / 0.08);
  padding: 4px 6px;
  border-radius: 0.2em;
  background-color: hsl(var(--base)) !important;
  -webkit-transition: 0.2s linear;
  transition: 0.2s linear;
  line-height: 25px;
  position: relative;
  margin-left: 15px;
  color: hsl(var(--text-color)) !important;
}

.form--control[type="file"]::file-selector-button {
  border: 1px solid hsl(var(--black) / 0.08);
  padding: 4px 6px;
  border-radius: 0.2em;
  background-color: hsl(var(--base)) !important;
  -webkit-transition: 0.2s linear;
  transition: 0.2s linear;
  line-height: 25px;
  position: relative;
  margin-left: 15px;
  color: hsl(var(--text-color)) !important;
}

.form--control[type="file"]::-webkit-file-upload-button:hover {
  background-color: hsl(var(--base));
  border: 1px solid hsl(var(--base));
  color: hsl(var(--black));
}

.form--control[type="file"]::file-selector-button:hover {
  background-color: hsl(var(--base));
  border: 1px solid hsl(var(--base));
  color: hsl(var(--black));
}

/* Form Control End */
textarea.form--control {
  height: 130px;
}

/* Autofill Css */
input:-webkit-autofill,
input:-webkit-autofill:hover,
input:-webkit-autofill:focus,
input:-webkit-autofill:active {
  -webkit-transition: background-color 5000s ease-in-out 0s;
  transition: background-color 5000s ease-in-out 0s;
}

input:-webkit-autofill,
textarea:-webkit-autofill,
select:-webkit-autofill {
  -webkit-box-shadow: 0 0 0px 1000px transparent inset;
  -webkit-text-fill-color: hsl(var(--black)) !important;
  caret-color: hsl(var(--black));
}

.footer-newsletter-form .form--control:-webkit-autofill {
  -webkit-text-fill-color: hsl(var(--white)) !important;
  caret-color: hsl(var(--white)) !important;
}

/* Autofill Css End */
/* input group */
.input--group {
  position: relative;
}

/* Show Hide Password */
input#your-password,
input#confirm-password {
  padding-right: 50px;
}

.password-show-hide {
  position: absolute;
  right: 20px;
  z-index: 5;
  cursor: pointer;
  top: 50%;
  -webkit-transform: translateY(-50%);
  transform: translateY(-50%);
  color: hsl(var(--black) / 0.4);
}

/* --------------- Number Arrow None --------------------- */
input::-webkit-outer-spin-button,
input::-webkit-inner-spin-button {
  -webkit-appearance: none;
}

input[type="number"] {
  -webkit-appearance: textfield;
  -moz-appearance: textfield;
  appearance: textfield;
}

/* Custom Checkbox Design */
.form--check a {
  display: inline;
}

.form--check .form-check-input {
  -webkit-box-shadow: none;
  box-shadow: none;
  background-color: transparent;
  box-shadow: none !important;
  border: 0;
  position: relative;
  border-radius: 0px;
  width: 16px;
  height: 16px;
  border: 1px solid hsl(var(--black) / 0.2);
  cursor: pointer;
}

.form--check .form-check-input:checked {
  background-color: hsl(var(--base)) !important;
  border-color: hsl(var(--base)) !important;
  -webkit-box-shadow: none;
  box-shadow: none;
}

.form--check .form-check-input:checked[type="checkbox"] {
  background-image: none;
}

.form--check .form-check-input:checked::before {
  position: absolute;
  content: "\f00c";
  font-family: "Font Awesome 5 Free";
  font-weight: 900;
  color: hsl(var(--white));
  font-size: 0.6875rem;
  top: 50%;
  left: 50%;
  -webkit-transform: translate(-50%, -50%);
  transform: translate(-50%, -50%);
}

.form--check .form-check-label {
  font-weight: 600;
  width: calc(100% - 16px);
  padding-left: 12px;
  cursor: pointer;
  font-size: 0.875rem;
}

@media screen and (max-width: 424px) {
  .form--check label {
    font-size: 0.9375rem;
  }
}

@media screen and (max-width: 424px) {
  .form--check a {
    font-size: 0.9375rem;
  }
}

/* Custom Radio Design */
.form--radio .form-check-input {
  -webkit-box-shadow: none;
  box-shadow: none;
  border: 1px solid hsl(var(--black) / 0.2);
  position: relative;
  background-color: transparent;
  cursor: pointer;
  width: 16px;
  height: 16px;
}

.form--radio .form-check-input:active {
  -webkit-filter: brightness(100%);
  filter: brightness(100%);
}

.form--radio .form-check-input:checked {
  background-color: transparent;
  border-color: hsl(var(--base));
}

.form--radio .form-check-input:checked[type="radio"] {
  background-image: none;
}

.form--radio .form-check-input:checked::before {
  position: absolute;
  content: "";
  top: 50%;
  left: 50%;
  -webkit-transform: translate(-50%, -50%);
  transform: translate(-50%, -50%);
  width: 7px;
  height: 7px;
  background-color: hsl(var(--base));
  border-radius: 50%;
  z-index: 999;
}

.form--radio .form-check-label {
  font-weight: 600;
  width: calc(100% - 16px);
  padding-left: 12px;
  cursor: pointer;
  font-size: 0.875rem;
}

/*  Custom Switch Design */
.form--switch .form-check-input {
  border-radius: 3px;
  background-image: none;
  position: relative;
  -webkit-box-shadow: none;
  box-shadow: none;
  border: 0;
  background-color: hsl(var(--black) / 0.2) !important;
  padding: 10px !important;
  margin-left: 0;
  margin-bottom: 5px;
  border-radius: 40px;
  width: 70px;
  height: 38px;
  cursor: pointer;
}

.form--switch .form-check-input:focus {
  border-radius: 40px;
  background-image: none;
  position: relative;
  -webkit-box-shadow: none;
  box-shadow: none;
  border: 0;
}

.form--switch .form-check-input::before {
  position: absolute;
  content: "";
  width: 28px;
  height: 28px;
  background-color: hsl(var(--white));
  top: 50%;
  -webkit-transform: translateY(-50%);
  transform: translateY(-50%);
  border-radius: 2px;
  left: 5px;
  border-radius: 50%;
  -webkit-transition: 0.2s linear;
  transition: 0.2s linear;
}

.form--switch .form-check-input:checked {
  background-color: hsl(var(--base)) !important;
}

.form--switch .form-check-input:checked::before {
  left: calc(100% - 33px);
  background-color: hsl(var(--white)) !important;
}

.form--switch .form-check-input:checked[type="checkbox"] {
  background-image: none;
}

.form--switch .form-check-label {
  width: calc(100% - 14px);
  padding-left: 5px;
  cursor: pointer;
}

/*  Custom Switch End Design */
/* ================================= Form Css End =========================== */
/* ================================= Modal Css Start =========================== */
.custom--modal .search-form-item {
  width: 100% !important;
}

.custom--modal .selected-option {
  padding: 18px 0px 18px 26px;
  border: 0;
  border-radius: 0;
  cursor: pointer;
  background-color: transparent;
}

@media screen and (max-width: 767px) {
  .custom--modal .selected-option {
    padding: 14px 0px 14px 26px;
  }
}

@media screen and (max-width: 575px) {
  .custom--modal .selected-option {
    padding: 12px 0px 12px 22px;
  }
}

.custom--modal .form--control {
  border: 1px solid hsl(var(--base) / 0.2) !important;
}

.custom--modal .modal-header {
  border-bottom: 0;
  padding: 0;
}

.custom--modal .modal-header .btn-close {
  width: 28px;
  height: 28px;
  background-color: white;
  color: hsL(var(--base-two) / 0.6) !important;
  font-size: 14px;
  line-height: 1;
  border-radius: 4px;
  -webkit-transition: 0.2s linear;
  transition: 0.2s linear;
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
  opacity: 1;
  border: 2px solid hsl(var(--base-two) / 0.6);
  position: absolute;
  top: 26px;
  right: 26px;
}

.custom--modal .modal-header .btn-close:focus {
  -webkit-box-shadow: none !important;
  box-shadow: none !important;
}

.custom--modal .modal-dialog {
  max-width: 600px;
}

.custom--modal .modal-content {
  border-radius: 10px !important;
  padding: 40px;
}

@media screen and (max-width: 767px) {
  .custom--modal .modal-content {
    padding: 24px;
  }
}

@media screen and (max-width: 575px) {
  .custom--modal .modal-content {
    padding: 24px 16px;
  }
}

.custom--modal .modal-body {
  padding: 0;
}

.custom--modal .modal-icon i {
  font-size: 2rem;
  color: hsl(var(--base));
  border: 3px solid hsl(var(--base));
  width: 50px;
  height: 50px;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  justify-content: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  border-radius: 50%;
}

.custom--modal .modal-footer {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
  -ms-flex-negative: 0;
  flex-shrink: 0;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  justify-content: center;
  padding: 0;
  padding-top: 0;
  border-top: 0;
  -webkit-box-pack: end;
  -ms-flex-pack: end;
  justify-content: flex-end;
}

.datepicker-here {
  border: 0 !important;
}

/* ================================= Modal Css End =========================== */
/* ================================= Pagination Css Start =========================== */
.pagination {
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  justify-content: center;
  margin-top: 40px;
}

@media screen and (max-width: 991px) {
  .pagination {
    margin-top: 32px;
  }
}

.pagination .page-item.active .page-link {
  background-color: hsl(var(--base));
  color: hsl(var(--white));
  border-color: hsl(var(--base));
}

.pagination .page-item .page-link {
  border: 1px solid hsl(var(--base-two) / 0.15);
  margin: 0 4px;
  border-radius: 8px;
  height: 46px;
  width: 46px;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  justify-content: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  background-color: transparent;
  font-weight: 600;
  padding: 0;
  color: hsl(var(--body-color));
  background: hsl(var(--white));
}

@media screen and (max-width: 575px) {
  .pagination .page-item .page-link {
    height: 36px;
    width: 36px;
    font-size: 14px;
  }
}

.pagination .page-item .page-link:hover {
  background-color: hsl(var(--base));
  color: hsl(var(--white));
  border-color: hsl(var(--base));
}

.pagination .page-item .page-link:focus {
  -webkit-box-shadow: none;
  box-shadow: none;
}

/* ================================= Pagination Css End =========================== */
/* ================================= Table Css Start =========================== */
.action-buttons {
  gap: 10px;
  -webkit-box-pack: end;
  -ms-flex-pack: end;
  justify-content: flex-end;
}

.action-btn {
  width: 35px;
  height: 35px;
  border-radius: 50%;
}

.edit-btn {
  color: hsl(var(--info));
  background-color: hsl(var(--info) / 0.08);
}

.delete-btn {
  color: hsl(var(--danger));
  background-color: hsl(var(--danger) / 0.08);
}

.table {
  margin: 0;
  border-collapse: collapse;
  border-collapse: separate;
  border-spacing: 0px 0px;
}

.table thead tr th {
  background-color: hsl(var(--white));
  text-align: center;
  padding: 12px 0;
  color: hsl(var(--base-two) / 0.6);
  font-family: var(--heading-font);
  font-weight: 700;
  border-bottom: 0;
  max-width: 170px;
  font-size: 0.875rem;
  border-bottom: 1px solid hsl(var(--base-two) / 0.1);
}

.table thead tr th:first-child {
  text-align: left;
}

.table thead tr th:last-child {
  text-align: right;
}

.table tbody {
  border: 0 !important;
  background-color: hsl(var(--white));
}

.table tbody tr:last-child {
  border-bottom: 0;
}

.table tbody tr td {
  text-align: center;
  vertical-align: middle;
  padding: 12px 0;
  border-width: 1px;
  border: 0;
  font-family: var(--heading-font);
  color: hsl(var(--base-two) / 0.6);
  font-weight: 600;
  max-width: 170px;
  font-size: 0.8125rem;
  border-bottom: 1px solid hsl(var(--black) / 0.08);
}

.table tbody tr td::before {
  content: attr(data-label);
  font-family: var(--heading-font);
  font-size: 0.9375rem;
  color: hsl(var(--black));
  font-weight: 500;
  display: none;
  width: 45% !important;
  text-align: left;
  font-weight: 600;
}

.table tbody tr td:first-child {
  text-align: left;
  font-size: 0.875rem;
}

.table tbody tr td:last-child {
  text-align: right;
}

@media screen and (max-width: 767px) {
  .table--responsive--md thead {
    display: none;
  }

  .table--responsive--md tbody tr {
    display: block;
  }

  .table--responsive--md tbody tr:last-child td {
    border-bottom: 0;
  }

  .table--responsive--md tbody tr td {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    -webkit-box-pack: justify;
    -ms-flex-pack: justify;
    justify-content: space-between;
    gap: 15px;
    text-align: right;
    padding: 10px 15px;
    border: none;
    border-bottom: 1px solid hsl(var(--black) / 0.8);
    max-width: unset;
  }

  .table--responsive--md tbody tr td:last-child {
    border: none;
  }

  .table--responsive--md tbody tr td:first-child {
    text-align: right;
    border-left: 0;
  }

  .table--responsive--md tbody tr td::before {
    display: block;
    font-size: 0.875rem;
    color: hsl(var(--black) / 0.7);
  }
}

@media screen and (max-width: 767px) {
  .table--responsive--md tbody tr td {
    border: 0;
  }
}

@media screen and (max-width: 991px) {
  .table--responsive--lg thead {
    display: none;
  }

  .table--responsive--lg tbody tr {
    display: block;
  }

  .table--responsive--lg tbody tr:nth-child(even) {
    background-color: hsl(var(--black) / 0.02);
  }

  .table--responsive--lg tbody tr:last-child td {
    border-bottom: 0;
  }

  .table--responsive--lg tbody tr td {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    -webkit-box-pack: justify;
    -ms-flex-pack: justify;
    justify-content: space-between;
    gap: 15px;
    text-align: right;
    padding: 10px 15px;
    border: none;
    border-bottom: 1px solid hsl(var(--black) / 0.08);
    max-width: unset;
  }

  .table--responsive--lg tbody tr td:last-child {
    border: none;
  }

  .table--responsive--lg tbody tr td:first-child {
    text-align: right;
    border-left: 0;
  }

  .table--responsive--lg tbody tr td::before {
    display: block;
    font-size: 0.875rem;
    color: hsl(var(--black) / 0.7);
  }
}

@media screen and (max-width: 991px) {
  .table--responsive--lg tbody tr td {
    border: none;
  }
}

@media screen and (max-width: 1199px) {
  .table--responsive--xl thead {
    display: none;
  }

  .table--responsive--xl tbody tr {
    display: block;
  }

  .table--responsive--xl tbody tr:nth-child(even) {
    background-color: hsl(var(--black) / 0.02);
  }

  .table--responsive--xl tbody tr td {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    -webkit-box-pack: justify;
    -ms-flex-pack: justify;
    justify-content: space-between;
    gap: 15px;
    text-align: right;
    padding: 10px 15px;
    border: none;
    border-bottom: 1px solid hsl(var(--black) / 0.08);
    max-width: unset;
  }

  .table--responsive--xl tbody tr td:last-child {
    border: none;
  }

  .table--responsive--xl tbody tr td:first-child {
    text-align: right;
    border-left: 0;
  }

  .table--responsive--xl tbody tr td::before {
    display: block;
    font-size: 0.875rem;
    color: hsl(var(--black) / 0.7);
  }
}

@media screen and (max-width: 1199px) {
  .table--responsive--xl tbody tr td {
    border: 0;
  }
}

@media screen and (max-width: 1399px) {
  .table--responsive--xxl thead {
    display: none;
  }

  .table--responsive--xxl tbody tr {
    display: block;
  }

  .table--responsive--xxl tbody tr:last-child td {
    border-bottom: 0;
  }

  .table--responsive--xxl tbody tr td {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    -webkit-box-pack: justify;
    -ms-flex-pack: justify;
    justify-content: space-between;
    gap: 15px;
    text-align: right;
    padding: 10px 15px;
    border: none;
    border-bottom: 1px solid hsl(var(--black) / 0.08);
    max-width: unset;
  }

  .table--responsive--xxl tbody tr td:last-child {
    border: none;
  }

  .table--responsive--xxl tbody tr td:first-child {
    text-align: right;
    border-left: 0;
  }

  .table--responsive--xxl tbody tr td::before {
    display: block;
    font-size: 0.875rem;
    color: hsl(var(--black) / 0.7);
  }
}

@media screen and (max-width: 1399px) {
  .table--responsive--xxl tbody tr td {
    border: 0;
  }
}

/* ================================= Table Css End =========================== */
/* ================================= Tab Css Start =========================== */
.custom--tab {
  -webkit-box-pack: start;
  -ms-flex-pack: start;
  justify-content: flex-start;
  border-radius: 8px;
  background-color: hsl(var(--white));
  margin-bottom: 30px;
  border: 1px solid hsl(var(--base-two) / 0.05);
  position: relative;
  overflow: hidden;
}

.custom--tab .tab__bar {
  position: absolute;
  content: "";
  bottom: 0;
  height: 2px;
  background: var(--gradient-color);
  -webkit-transition: 0.3s;
  transition: 0.3s;
}

.custom--tab .nav-item {
  border-bottom: 0;
  position: static !important;
}

.custom--tab .nav-item .nav-link {
  color: hsl(var(--body-color));
  padding: 20px 25px;
  background-color: transparent !important;
  border-radius: 0;
  -webkit-transition: 0.4s;
  transition: 0.4s;
  border: 0 !important;
  font-size: 20px;
  font-weight: 500;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  margin-bottom: 0;
  line-height: 1;
}

@media screen and (max-width: 1199px) {
  .custom--tab .nav-item .nav-link {
    padding: 20px 16px !important;
    font-size: 18px;
  }
}

@media screen and (max-width: 767px) {
  .custom--tab .nav-item .nav-link {
    font-size: 16px;
  }
}

@media screen and (max-width: 575px) {
  .custom--tab .nav-item .nav-link {
    font-size: 14px;
    padding: 16px 10px !important;
  }
}

.custom--tab .nav-item .nav-link.active {
  color: hsl(var(--base-two));
}

/* ================================= Tab Css End =========================== */
/* ================================= Badge Css Start =========================== */
.custom--badge {
  font-size: 0.75rem;
  border-radius: 5px;
  padding: 8px 10px;
  font-weight: 500;
  position: relative;
  text-align: center;
}

.badge--base {
  background-color: hsl(var(--base)) !important;
  color: hsl(var(--white)) !important;
}

.badge--primary {
  background-color: hsl(var(--primary)) !important;
  color: hsl(var(--white)) !important;
}

.badge--secondary {
  background-color: hsl(var(--secondary)) !important;
  color: hsl(var(--white)) !important;
}

.badge--success {
  background-color: hsl(var(--success)) !important;
  color: hsl(var(--white)) !important;
}

.badge--danger {
  background-color: hsl(var(--danger)) !important;
  color: hsl(var(--white)) !important;
}

.badge--warning {
  background-color: hsl(var(--warning)) !important;
  color: hsl(var(--white)) !important;
}

.badge--info {
  background-color: hsl(var(--info)) !important;
  color: hsl(var(--white)) !important;
}

.badge--dark {
  background-color: hsl(var(--black)) !important;
  color: hsl(var(--white)) !important;
}

/* ================================= Badge Css End =========================== */
/* ====================================== Alert Css Start =============================== */
.alert {
  margin-bottom: 0;
  background-color: hsl(var(--white)) !important;
  font-weight: 400;
  padding: 17px 24px;
  border-radius: 5px;
}

@media screen and (max-width: 991px) {
  .alert {
    padding: 16px;
  }
}

@media screen and (max-width: 575px) {
  .alert {
    padding: 12px;
  }
}

.alert__icon {
  font-size: 1.5rem;
  line-height: 1;
}

.alert__content {
  width: calc(100% - 24px);
  padding-left: 16px;
}

@media screen and (max-width: 991px) {
  .alert__content {
    padding-left: 12px;
  }
}

@media screen and (max-width: 575px) {
  .alert__content {
    padding-left: 0;
    width: 100%;
    margin-top: 6px;
  }
}

.alert__title {
  font-size: 1rem;
  color: hsl(var(--base-two) / 0.8);
  font-weight: 600;
  font-family: var(--heading-font);
  margin-bottom: 6px;
}

.alert__desc {
  color: hsl(var(--base-two) / 0.5);
  display: block;
  line-height: 1.375;
}

@media screen and (max-width: 424px) {
  .alert__desc {
    font-size: 0.8125rem;
  }
}

.alert__link {
  position: relative;
}

.alert__link:hover::before {
  visibility: visible;
  opacity: 1;
  bottom: 0;
}

.alert__link::before {
  left: 0;
  bottom: -5px;
  width: 100%;
  height: 1px;
  background-color: hsl(var(--base));
  visibility: hidden;
  opacity: 0;
  -webkit-transition: 0.2s ease-in-out;
  transition: 0.2s ease-in-out;
}

.alert--base {
  border-color: hsl(var(--base) / 0.6);
}

.alert--base .alert__icon {
  color: hsl(var(--base));
}

.alert--primary {
  border-color: hsl(var(--primary) / 0.6);
}

.alert--primary .alert__icon {
  color: hsl(var(--primary));
}

.alert--success {
  border-color: hsl(var(--success) / 0.6);
}

.alert--success .alert__icon {
  color: hsl(var(--success));
}

.alert--info {
  border-color: hsl(var(--info) / 0.6);
}

.alert--info .alert__icon {
  color: hsl(var(--info));
}

.alert--danger {
  border-color: hsl(var(--danger) / 0.6);
}

.alert--danger .alert__icon {
  color: hsl(var(--danger));
}

.alert--warning {
  border-color: hsl(var(--warning) / 0.6);
}

.alert--warning .alert__icon {
  color: hsl(var(--warning));
}

.alert--secondary {
  border-color: hsl(var(--secondary) / 0.6);
}

.alert--secondary .alert__icon {
  color: hsl(var(--secondary));
}

/* ====================================== Alert Css End =============================== */
/* =========================== Banner Section Start Here ========================= */
.banner-section {
  position: relative;
  z-index: 1;
}

.banner-section__shape {
  position: absolute;
  top: -84px;
  right: 0;
  width: 53%;
  height: 100%;
  z-index: -1;
}

.banner-section__shape img {
  height: 100%;
  width: 100%;
}

@media screen and (max-width: 991px) {
  .banner-section__shape {
    display: none;
  }
}

@media screen and (max-width: 991px) {
  .banner-content {
    text-align: center;
  }
}

.banner-content__heading {
  background: var(--gradient-color);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  margin-bottom: 0.25rem;
  font-weight: 500;
  font-style: italic;
}

.banner-content__heading::-moz-selection {
  background-color: hsl(var(--base));
  -webkit-text-fill-color: hsl(var(--white));
}

.banner-content__heading::selection {
  background-color: hsl(var(--base));
  -webkit-text-fill-color: hsl(var(--white));
}

.banner-content__title {
  font-weight: 900;
  margin-bottom: 1.5rem;
}

@media screen and (max-width: 575px) {
  .banner-content__title {
    margin-bottom: 1rem;
  }
}

.banner-content__title .text {
  background: var(--gradient-color);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

.banner-content__title .text::-moz-selection {
  background-color: hsl(var(--base));
  -webkit-text-fill-color: hsl(var(--white));
}

.banner-content__title .text::selection {
  background-color: hsl(var(--base));
  -webkit-text-fill-color: hsl(var(--white));
}

.banner-content__desc {
  max-width: 29.6875rem;
  width: 100%;
  font-size: 1.25rem;
}

@media screen and (max-width: 991px) {
  .banner-content__desc {
    font-size: 1.125rem;
    margin: 0 auto;
  }
}

@media screen and (max-width: 767px) {
  .banner-content__desc {
    font-size: 1rem;
  }
}

.banner-content__button {
  margin-top: 2.5rem;
}

@media screen and (max-width: 991px) {
  .banner-content__button {
    margin-top: 1.875rem;
  }
}

@media screen and (max-width: 575px) {
  .banner-content__button {
    margin-top: 1rem;
  }
}

/* =========================== Banner Section End Here ========================= */
.banner-slider {
  position: relative;
  right: -85px;
}

@media screen and (max-width: 1599px) {
  .banner-slider {
    right: -50px;
  }
}

@media screen and (max-width: 1499px) {
  .banner-slider {
    right: 0;
  }
}

.banner-slider .slick-initialized.slick-slider {
  margin: 0 0;
}

.banner-slider .slick-item {
  height: 100%;
  width: 100%;
  display: -webkit-box !important;
  display: -ms-flexbox !important;
  display: flex !important;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  justify-content: center;
  padding: 0px 40px;
}

@media screen and (max-width: 575px) {
  .banner-slider .slick-item {
    padding: 0;
  }
}

.banner-slider .slick-arrow {
  color: hsl(var(--base-two) / 0.5);
  width: 56px;
  height: 56px;
  background-color: hsl(var(--white));
  font-size: 18px;
  -webkit-filter: drop-shadow(0px 2.986px 2.986px hsl(var(--black) / 0.1));
  filter: drop-shadow(0px 2.986px 2.986px hsl(var(--black) / 0.1));
}

@media screen and (max-width: 1399px) {
  .banner-slider .slick-arrow {
    width: 50px;
    height: 50px;
    font-size: 16px;
  }
}

@media screen and (max-width: 991px) {
  .banner-slider .slick-arrow {
    width: 40px;
    height: 40px;
    font-size: 16px;
  }
}

.banner-slider .slick-arrow:hover {
  background-color: hsl(var(--base));
  color: hsl(var(--white));
}

.banner-slider .slick-dots li button {
  background: hsl(var(--white));
  color: hsl(var(--white));
  margin: 4px;
  width: 70px;
  height: 54px;
  border-radius: 12px;
  text-indent: unset;
  border: 1px solid hsl(var(--secondary));
}

.banner-slider .slick-dots li button img {
  max-height: 36px;
}

@media screen and (max-width: 575px) {
  .banner-slider .slick-dots li button {
    width: 60px;
    height: 46px;
    border-radius: 6px;
  }

  .banner-slider .slick-dots li button img {
    max-height: 28px;
  }
}

@media screen and (max-width: 424px) {
  .banner-slider .slick-dots li button {
    width: 46px;
    height: 36px;
    margin: 2px;
  }

  .banner-slider .slick-dots li button img {
    max-height: 24px;
  }
}

.banner-slider .slick-dots li.slick-active button {
  background: var(--gradient-color);
}

/* ================= Slick Arrow & Dots css Start ================ */
.offer-card {
  border-radius: 12px;
  border: 1px solid hsl(var(--base) / 0.1);
  background: hsl(var(--white));
  overflow: hidden;
}

.offer-card__top {
  position: relative;
}

.offer-card__thumb {
  border-radius: 12px;
  width: 100%;
  -o-object-fit: cover;
  object-fit: cover;
}

.offer-card__content {
  position: absolute;
  top: 20px;
  left: 20px;
  background-color: hsl(var(--base));
  color: hsl(var(--white));
  padding: 14px 12px;
  border-radius: 8px;
}

@media screen and (max-width: 1199px) {
  .offer-card__content {
    padding: 10px 14px;
    top: 16px;
    left: 16px;
  }
}

.offer-card__content .text {
  font-size: 0.75rem;
  font-weight: 600;
  display: block;
}

@media screen and (max-width: 1199px) {
  .offer-card__content .text {
    font-size: 0.625rem;
  }
}

.offer-card__value {
  margin-bottom: 0;
  font-weight: 700;
  color: hsl(var(--white));
  line-height: 1;
}

@media screen and (max-width: 1199px) {
  .offer-card__value {
    font-size: 1.5rem;
  }
}

@media screen and (max-width: 575px) {
  .offer-card__value {
    font-size: 1.25rem;
  }
}

.offer-card__link {
  padding: 22px 18px;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: justify;
  -ms-flex-pack: justify;
  justify-content: space-between;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
  gap: 12px;
}

@media screen and (max-width: 1199px) {
  .offer-card__link {
    padding: 16px;
  }
}

@media screen and (max-width: 575px) {
  .offer-card__link {
    padding: 10px 12px;
  }
}

.offer-card__link .button {
  width: 32px;
  height: 32px;
  -ms-flex-negative: 0;
  flex-shrink: 0;
  border-radius: 8px;
  display: grid;
  place-content: center;
  font-size: 18px;
  color: hsl(var(--white));
  position: relative;
  z-index: 1;
  overflow: hidden;
}

@media screen and (max-width: 575px) {
  .offer-card__link .button {
    height: 30px;
    width: 30px;
    font-size: 16px;
  }
}

.offer-card__link .button::after,
.offer-card__link .button::before {
  content: "";
  position: absolute;
  -webkit-transition: all linear 0.3s;
  transition: all linear 0.3s;
  background: var(--gradient-color);
  height: 100%;
  width: 100%;
  top: 0;
  left: 0;
  z-index: -1;
}

.offer-card__link .button::after {
  opacity: 0;
  background: -webkit-gradient(
    linear,
    left top,
    left bottom,
    from(hsl(var(--base))),
    to(hsl(var(--secondary)))
  );
  background: linear-gradient(
    180deg,
    hsl(var(--base)) 0%,
    hsl(var(--secondary)) 100%
  );
}

.offer-card__link:hover .offer-card__title {
  color: hsl(var(--base));
}

.offer-card__link:hover .button::after {
  opacity: 1;
}

.offer-card__title {
  margin-bottom: 0;
  -webkit-transition: all linear 0.3s;
  transition: all linear 0.3s;
  -webkit-box-flex: 1;
  -ms-flex: 1;
  flex: 1;
}

.work-step-wrapper {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: start;
  -ms-flex-align: start;
  align-items: flex-start;
  gap: 12px;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
}

@media screen and (max-width: 1199px) {
  .work-step-wrapper {
    gap: 8px;
  }
}

@media screen and (max-width: 991px) {
  .work-step-wrapper {
    gap: 24px;
  }
}

@media screen and (max-width: 575px) {
  .work-step-wrapper {
    gap: 24px 12px;
  }
}

@media screen and (max-width: 424px) {
  .work-step-wrapper {
    -webkit-box-pack: center;
    -ms-flex-pack: center;
    justify-content: center;
  }
}

.work-step-shape {
  width: 3.75rem;
  -ms-flex-negative: 0;
  flex-shrink: 0;
  -webkit-mask-image: url(../images/thumbs/step-arrow.png);
  -webkit-mask-position: center;
  -webkit-mask-size: cover;
  -webkit-mask-repeat: no-repeat;
  background: var(--gradient-color);
}

@media screen and (max-width: 1399px) {
  .work-step-shape {
    width: 3.125rem;
  }
}

@media screen and (max-width: 1199px) {
  .work-step-shape {
    width: 2.5rem;
  }
}

@media screen and (max-width: 991px) {
  .work-step-shape:nth-of-type(4) {
    display: none;
  }
}

@media screen and (max-width: 424px) {
  .work-step-shape {
    -webkit-transform: rotate(85deg);
    transform: rotate(85deg);
  }

  .work-step-shape:nth-of-type(4) {
    display: block;
  }
}

.work-step-shape img {
  opacity: 0;
}

.work-step-card {
  width: calc(25% - 63px);
}

@media screen and (max-width: 1399px) {
  .work-step-card {
    width: calc(25% - 56px);
  }
}

@media screen and (max-width: 1199px) {
  .work-step-card {
    width: calc(25% - 42px);
  }
}

@media screen and (max-width: 991px) {
  .work-step-card {
    width: calc(50% - 44px);
    margin-top: 16px;
  }
}

@media screen and (max-width: 575px) {
  .work-step-card {
    width: calc(50% - 32px);
  }
}

@media screen and (max-width: 424px) {
  .work-step-card {
    width: 100%;
  }
}

.work-step-card__title {
  text-align: center;
  margin-bottom: 16px;
}

@media screen and (max-width: 1199px) {
  .work-step-card__title {
    margin-bottom: 12px;
  }
}

.work-step-card__desc {
  text-align: center;
}

@media screen and (max-width: 1199px) {
  .work-step-card__desc {
    font-size: 0.875rem;
  }
}

.work-step-card__top {
  position: relative;
  height: 5.125rem;
  width: 5.125rem;
  background: var(--gradient-color);
  display: grid;
  place-content: center;
  border-radius: 8px;
  margin: 0 auto 24px;
}

@media screen and (max-width: 1399px) {
  .work-step-card__top {
    height: 4.375rem;
    width: 4.375rem;
  }
}

@media screen and (max-width: 1199px) {
  .work-step-card__top {
    height: 3.75rem;
    width: 3.75rem;
  }
}

.work-step-card__top .icon {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  font-size: 2.625rem;
  color: hsl(var(--white));
}

@media screen and (max-width: 1399px) {
  .work-step-card__top .icon {
    font-size: 2.25rem;
  }
}

.work-step-card__top .count {
  position: absolute;
  height: 2.5rem;
  width: 2.5rem;
  border: 1px solid hsl(var(--secondary));
  display: grid;
  place-content: center;
  border-radius: 50%;
  background-color: hsl(var(--white));
  color: hsl(var(--base-two));
  font-weight: 700;
  top: -16px;
  right: -20px;
}

@media screen and (max-width: 1199px) {
  .work-step-card__top .count {
    height: 2rem;
    width: 2rem;
    font-size: 14px;
    right: -16px;
  }
}

.our-vehicle {
  position: relative;
  z-index: 1;
}

.our-vehicle::after {
  content: "";
  position: absolute;
  height: 100%;
  width: 100%;
  top: 0;
  left: 0;
  background-color: hsl(var(--base-two) / 0.8);
  z-index: -1;
}

.vehicle-card {
  border-radius: 16px;
  background: hsl(var(--white));
  overflow: hidden;
  -webkit-box-shadow: 0px 4px 21px 0px hsl(var(--base-two) / 0.15);
  box-shadow: 0px 4px 21px 0px hsl(var(--base-two) / 0.15);
  height: 100%;
}

.vehicle-card:hover .vehicle-card__name {
  color: hsl(var(--base));
}

.vehicle-card:hover .vehicle-card__thumb img {
  -webkit-transform: scale(1.1);
  transform: scale(1.1);
}

.vehicle-card__thumb {
  position: relative;
  overflow: hidden;
}

.vehicle-card__thumb img {
  width: 100%;
  -o-object-fit: cover;
  object-fit: cover;
  -webkit-transition: all linear 0.3s;
  transition: all linear 0.3s;
}

.vehicle-card__thumb::after {
  content: "";
  position: absolute;
  height: 40px;
  width: 100%;
  bottom: 0;
  left: 0;
  background: -webkit-gradient(
    linear,
    left top,
    left bottom,
    color-stop(10.98%, hsl(var(--white) / 0)),
    to(hsl(var(--white)))
  );
  background: linear-gradient(
    180deg,
    hsl(var(--white) / 0) 10.98%,
    hsl(var(--white)) 100%
  );
}

.vehicle-card__content {
  padding: 16px 18px 20px;
}

@media screen and (max-width: 1199px) {
  .vehicle-card__content {
    padding: 16px;
  }
}

.vehicle-card__name {
  margin-bottom: 8px;
  -webkit-transition: all linear 0.3s;
  transition: all linear 0.3s;
}

.vehicle-card__desc {
  font-weight: 500;
  color: hsl(var(--body-color));
}

.benifit {
  position: relative;
  z-index: 1;
}

.benifit-thumb {
  position: absolute;
  height: 100%;
  width: 32%;
  left: 0;
  top: -40px;
  z-index: 2;
}

@media screen and (max-width: 1399px) {
  .benifit-thumb {
    width: 40%;
  }
}

@media screen and (max-width: 1199px) {
  .benifit-thumb {
    width: 46%;
  }
}

.benifit-thumb img {
  width: 100%;
  height: 100%;
  -o-object-fit: contain;
  object-fit: contain;
  -o-object-position: left;
  object-position: left;
}

@media screen and (max-width: 767px) {
  .benifit-thumb {
    position: static;
    width: 100%;
  }
}

@media screen and (max-width: 1199px) {
  .benifit-wrapper {
    padding: 0;
    border: 0;
  }
}

.benifit-shape {
  position: absolute;
  top: 0;
  right: 0;
  z-index: -1;
}

@media screen and (max-width: 575px) {
  .benifit .section-heading {
    text-align: center;
  }
}

.benifit-card {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: start;
  -ms-flex-pack: start;
  justify-content: flex-start;
  gap: 16px;
}

@media screen and (max-width: 575px) {
  .benifit-card {
    -webkit-box-orient: vertical;
    -webkit-box-direction: normal;
    -ms-flex-direction: column;
    flex-direction: column;
    text-align: center;
  }
}

.benifit-card__icon {
  width: 76px;
  height: 76px;
  display: grid;
  place-content: center;
  border-radius: 8px;
  background: var(--gradient-color);
  font-size: 2.5rem;
  -ms-flex-negative: 0;
  flex-shrink: 0;
}

@media screen and (max-width: 1199px) {
  .benifit-card__icon {
    height: 60px;
    width: 60px;
    font-size: 2rem;
  }
}

@media screen and (max-width: 991px) {
  .benifit-card__icon {
    height: 54px;
    width: 54px;
    font-size: 1.75rem;
  }
}

.benifit-card__content {
  -webkit-box-flex: 1;
  -ms-flex: 1;
  flex: 1;
}

.benifit-card__title {
  margin-bottom: 8px;
  font-weight: 600;
  line-height: 1;
}

@media screen and (max-width: 767px) {
  .benifit-card__title {
    font-size: 14px;
    font-weight: 700;
    margin-bottom: 4px;
  }
}

.benifit-card__desc {
  font-weight: 500;
}

@media screen and (max-width: 1399px) {
  .benifit-card__desc {
    font-size: 15px;
    line-height: 1.2;
    font-weight: 400;
  }
}

@media screen and (max-width: 991px) {
  .benifit-card__desc {
    font-size: 13px;
    font-weight: 500;
  }
}

.choose-us {
  position: relative;
  z-index: 1;
}

.choose-us::after {
  content: "";
  position: absolute;
  height: 100%;
  width: 100%;
  top: 0;
  left: 0;
  background-color: hsl(var(--base-two) / 0.8);
  z-index: -1;
}

.nav-tabs.style-two {
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  justify-content: center;
  border-radius: 0;
  border: 0;
  max-width: 1076px;
  width: 100%;
  margin: 0 auto 40px;
  gap: 10px;
}

@media screen and (max-width: 575px) {
  .nav-tabs.style-two {
    gap: 16px;
  }
}

@media screen and (max-width: 424px) {
  .nav-tabs.style-two {
    gap: 12px;
  }
}

.nav-tabs.style-two .nav-item {
  border-bottom: 0;
  padding: 0;
  width: 16.6666666667%;
  position: relative;
}

@media screen and (max-width: 575px) {
  .nav-tabs.style-two .nav-item {
    width: calc(33.3333333333% - 12px);
  }
}

@media screen and (max-width: 424px) {
  .nav-tabs.style-two .nav-item {
    width: calc(33.3333333333% - 8px);
  }
}

.nav-tabs.style-two .nav-item .nav-link {
  color: hsl(var(--base-two));
  padding: 40px 20px;
  background: hsl(var(--white)) !important;
  border-radius: 8px;
  -webkit-transition: unset;
  transition: unset;
  border: 0 !important;
  width: 100%;
  position: relative;
  z-index: 1;
  display: block !important;
  height: 100%;
}

@media screen and (max-width: 1199px) {
  .nav-tabs.style-two .nav-item .nav-link {
    padding: 32px 12px;
  }
}

@media screen and (max-width: 991px) {
  .nav-tabs.style-two .nav-item .nav-link {
    padding: 16px 8px !important;
  }
}

@media screen and (max-width: 767px) {
  .nav-tabs.style-two .nav-item .nav-link {
    padding: 10px 8px !important;
  }
}

.nav-tabs.style-two .nav-item .nav-link::after {
  content: "";
  position: absolute;
  height: 20px;
  width: 20px;
  background-color: hsl(var(--base));
  top: calc(100% - 12px);
  left: 50%;
  -webkit-transform: translate(-50%) rotate(-45deg);
  transform: translate(-50%) rotate(-45deg);
  display: none;
  border-radius: 4px;
}

.nav-tabs.style-two .nav-item .nav-link.active {
  background: var(--gradient-color) !important;
  color: hsl(var(--white));
  border: 0;
}

.nav-tabs.style-two .nav-item .nav-link.active::after {
  display: block;
}

@media screen and (max-width: 575px) {
  .nav-tabs.style-two .nav-item .nav-link.active::after {
    display: none;
  }
}

.nav-tabs.style-two .nav-item .nav-link.active:hover {
  color: hsl(var(--white));
}

.nav-tabs.style-two .nav-item .nav-link:hover {
  color: hsl(var(--base-two));
}

.nav-tabs.style-two .nav-item .nav-link .icon {
  font-size: 70px;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  justify-content: center;
  margin-bottom: 16px;
}

@media screen and (max-width: 1199px) {
  .nav-tabs.style-two .nav-item .nav-link .icon {
    font-size: 50px;
    margin-bottom: 12px;
  }
}

@media screen and (max-width: 991px) {
  .nav-tabs.style-two .nav-item .nav-link .icon {
    font-size: 36px;
    margin-bottom: 8px;
  }
}

@media screen and (max-width: 767px) {
  .nav-tabs.style-two .nav-item .nav-link .icon {
    font-size: 28px;
    margin-bottom: 6px;
  }
}

@media screen and (max-width: 991px) {
  .nav-tabs.style-two .nav-item .nav-link .title {
    font-size: 14px;
    line-height: 1.4;
  }
}

@media screen and (max-width: 767px) {
  .nav-tabs.style-two .nav-item .nav-link .title {
    font-size: 12px;
    line-height: 1.2;
  }
}

.choose-thumb {
  border-radius: 16px;
  overflow: hidden;
}

.choose-content__title {
  color: hsl(var(--white));
  margin-bottom: 20px;
  line-height: 1;
}

.choose-content__desc {
  font-size: 18px;
  color: hsl(var(--white));
  font-weight: 500;
  line-height: 1.6;
}

@media screen and (max-width: 1399px) {
  .choose-content__desc {
    font-size: 16px;
  }
}

@media screen and (max-width: 1199px) {
  .choose-content__desc {
    font-size: 15px;
  }
}

@media screen and (max-width: 575px) {
  .choose-content__desc {
    font-size: 14px;
  }
}

.choose-content__desc:not(:last-child) {
  margin-bottom: 24px;
}

@media screen and (max-width: 1399px) {
  .choose-content__desc:not(:last-child) {
    margin-bottom: 16px;
  }
}

@media screen and (max-width: 1199px) {
  .choose-content__desc:not(:last-child) {
    margin-bottom: 12px;
  }
}

.choose-content__list {
  margin-top: 40px;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: justify;
  -ms-flex-pack: justify;
  justify-content: space-between;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
  gap: 24px;
}

@media screen and (max-width: 1399px) {
  .choose-content__list {
    margin-top: 32px;
  }
}

@media screen and (max-width: 1199px) {
  .choose-content__list {
    margin-top: 24px;
    gap: 16px;
  }
}

@media screen and (max-width: 575px) {
  .choose-content__list {
    gap: 12px;
  }
}

.choose-content__item {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  gap: 0 14px;
  width: calc(50% - 16px);
}

@media screen and (max-width: 575px) {
  .choose-content__item {
    width: calc(50% - 6px);
    gap: 0 6px;
  }
}

.choose-content__item .icon {
  background: var(--gradient-color);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  font-size: 20px;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
}

@media screen and (max-width: 575px) {
  .choose-content__item .icon {
    font-size: 18px;
  }
}

@media screen and (max-width: 424px) {
  .choose-content__item .icon {
    font-size: 16px;
  }
}

.choose-content__item .text {
  color: hsl(var(--white));
  font-weight: 600;
}

@media screen and (max-width: 575px) {
  .choose-content__item .text {
    font-size: 14px;
  }
}

@media screen and (max-width: 424px) {
  .choose-content__item .text {
    font-size: 12px;
  }
}

/* ================================ Testimonails Section Css Start ============================= */
.testimonials {
  position: relative;
  overflow: hidden;
}

.testimonials .slick-next {
  right: -80px;
}

@media screen and (max-width: 1599px) {
  .testimonials .slick-next {
    right: -70px;
  }
}

@media screen and (max-width: 1499px) {
  .testimonials .slick-next {
    right: -20px;
  }
}

.testimonials .slick-prev {
  left: -80px;
}

@media screen and (max-width: 1599px) {
  .testimonials .slick-prev {
    left: -70px;
  }
}

@media screen and (max-width: 1499px) {
  .testimonials .slick-prev {
    left: -20px;
  }
}

.testimonial-item {
  background-color: hsl(var(--white));
  border: 1px solid hsl(var(--base) / 0.2);
  padding: 32px;
  border-radius: 16px;
  position: relative;
  height: 100%;
}

@media screen and (max-width: 1399px) {
  .testimonial-item {
    padding: 32px 24px;
  }
}

@media screen and (max-width: 767px) {
  .testimonial-item {
    padding: 24px 16px;
  }
}

.testimonial-item__logo {
  margin-bottom: 48px;
}

@media screen and (max-width: 1399px) {
  .testimonial-item__logo {
    margin-bottom: 32px;
  }
}

@media screen and (max-width: 991px) {
  .testimonial-item__logo {
    margin-bottom: 24px;
  }
}

@media screen and (max-width: 767px) {
  .testimonial-item__logo {
    margin-bottom: 16px;
  }
}

.testimonial-item__info {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  gap: 20px;
}

@media screen and (max-width: 767px) {
  .testimonial-item__info {
    gap: 12px;
  }
}

.testimonial-item__thumb {
  width: 56px;
  height: 56px;
  border-radius: 50%;
  overflow: hidden;
  -ms-flex-negative: 0;
  flex-shrink: 0;
}

@media screen and (max-width: 767px) {
  .testimonial-item__thumb {
    width: 42px;
    height: 42px;
  }
}

.testimonial-item__details {
  -webkit-box-flex: 1;
  -ms-flex: 1;
  flex: 1;
}

.testimonial-item__name {
  color: hsl(var(--black));
  font-weight: 600;
  line-height: 150%;
}

.testimonial-item__designation {
  font-size: 16px;
  line-height: 150%;
  color: hsl(var(--black));
}

@media screen and (max-width: 767px) {
  .testimonial-item__designation {
    font-size: 14px;
  }
}

.testimonial-item__desc {
  color: hsl(var(--black));
  display: block;
  font-size: 18px;
  margin-bottom: 24px;
}

@media screen and (max-width: 991px) {
  .testimonial-item__desc {
    font-size: 16px;
    margin-bottom: 16px;
  }
}

/* ================================ Testimonails Section Css End ============================= */

.clt-section-wrapper {
  border-radius: 24px;
  padding: 105px 32px 75px;
  position: relative;
  z-index: 1;
  overflow: hidden;
}

@media screen and (max-width: 1199px) {
  .clt-section-wrapper {
    padding: 70px 32px 50px;
  }
}

@media screen and (max-width: 991px) {
  .clt-section-wrapper {
    padding: 50px 24px 45px;
  }
}

@media screen and (max-width: 575px) {
  .clt-section-wrapper {
    padding: 40px 16px 36px;
  }
}

.clt-section-wrapper::after {
  content: "";
  position: absolute;
  height: 100%;
  width: 100%;
  top: 0;
  left: 0;
  background: #321807;
  opacity: 0.5;
  z-index: -1;
}

.clt-content {
  max-width: 1024px;
  width: 100%;
  margin: 0 auto;
  text-align: center;
}

.clt-content__subheading {
  margin-bottom: 16px;
  color: hsl(var(--secondary));
}

@media screen and (max-width: 767px) {
  .clt-content__subheading {
    margin-bottom: 12px;
  }
}

.clt-content__heading {
  margin-bottom: 8px;
  font-size: 48px;
  font-weight: 900;
  color: hsl(var(--white));
}

@media screen and (max-width: 1199px) {
  .clt-content__heading {
    font-size: 36px;
  }
}

@media screen and (max-width: 991px) {
  .clt-content__heading {
    font-size: 28px;
  }
}

@media screen and (max-width: 767px) {
  .clt-content__heading {
    font-size: 24px;
  }
}

@media screen and (max-width: 575px) {
  .clt-content__heading {
    font-size: 20px;
  }
}

.clt-content__desc {
  font-size: 20px;
  color: hsl(var(--white));
}

@media screen and (max-width: 1199px) {
  .clt-content__desc {
    font-size: 18px;
  }
}

@media screen and (max-width: 767px) {
  .clt-content__desc {
    font-size: 16px;
  }
}

@media screen and (max-width: 575px) {
  .clt-content__desc {
    font-size: 14px;
  }
}

.clt-content__button {
  margin-top: 32px;
}

@media screen and (max-width: 991px) {
  .clt-content__button {
    margin-top: 24px;
  }
}

@media screen and (max-width: 767px) {
  .clt-content__button {
    margin-top: 16px;
  }
}

.cta-contact {
  background: var(--gradient-color);
}

.cta-contact__bnt {
  margin-bottom: 16px;
}
.cta-contact-info-left {
  max-width: 800px;
}
.cta-contact-info__subtitle {
  font-size: 20px;
}

.cta-contact-info__btn {
  margin-top: 30px;
}

.cta-contact-info__list {
  margin-top: 30px;
  display: flex;
  flex-wrap: wrap;
  gap: 15px;
}
.cta-contact-info__list li {
  font-weight: 500;
  font-size: 1rem;
}

@media (max-width: 767px) {
  .cta-contact-info__subtitle {
    font-size: 16px;
  }
}
.cta-contact-info__title {
  font-size: 14px;
  margin-bottom: 6px;
  color: hsl(var(--base-two));
  font-weight: 600;
}

.contact-info-item {
  width: 100%;
  margin: 0 auto;
  text-align: center;
  padding: 20px 16px;
  border-radius: 12px;
  background-color: hsl(var(--base) / 0.1);
  height: 100%;
}

.contact-info-item__icon {
  background: var(--gradient-color);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  font-size: 36px;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  justify-content: center;
  margin-bottom: 16px;
}

.contact-info-item__title {
  margin-bottom: 12px;
}

.contact-info-item__desc {
  color: hsl(var(--base-two));
  margin-bottom: 16px;
}

.contact-info-item__text {
  color: hsl(var(--base-two));
  font-weight: 500;
  line-height: 150%;
  text-decoration-line: underline;
}

.contact-form-heading {
  margin-bottom: 30px;
}

@media screen and (max-width: 991px) {
  .contact-form-heading {
    margin-bottom: 24px;
  }
}

@media screen and (max-width: 767px) {
  .contact-form-heading {
    margin-bottom: 16px;
  }
}

.contact-form-heading__title {
  margin-bottom: 24px;
}

@media screen and (max-width: 991px) {
  .contact-form-heading__title {
    margin-bottom: 16px;
  }
}

@media screen and (max-width: 767px) {
  .contact-form-heading__title {
    margin-bottom: 12px;
  }
}

.contact-form-heading__desc {
  font-size: 18px;
}

@media screen and (max-width: 991px) {
  .contact-form-heading__desc {
    font-size: 16px;
  }
}

.contact-map {
  height: 100%;
}

.contact-map iframe {
  height: 100%;
  width: 100%;
  border-radius: 16px;
}

@media screen and (max-width: 991px) {
  .contact-map iframe {
    height: 350px;
  }
}

.blog {
  background-color: hsl(var(--base-two) / 0.03);
}

.blog .slick-initialized.slick-slider {
  margin: 0 -16px;
}

@media screen and (max-width: 1199px) {
  .blog .slick-initialized.slick-slider {
    margin: 0 -8px;
  }
}

@media screen and (max-width: 575px) {
  .blog .slick-initialized.slick-slider {
    padding: 0 -6px;
  }
}

.blog .slick-initialized.slick-slider .slick-arrow {
  top: calc(50% - 24px);
}

.blog .slick-initialized.slick-slider .slick-slide {
  height: auto;
  padding: 0 16px;
}

@media screen and (max-width: 1199px) {
  .blog .slick-initialized.slick-slider .slick-slide {
    padding: 0 8px;
  }
}

@media screen and (max-width: 575px) {
  .blog .slick-initialized.slick-slider .slick-slide {
    padding: 0 6px;
  }
}

.blog .slick-initialized.slick-slider .slick-slide > div {
  height: auto;
}

.blog .slick-initialized.slick-slider .slick-slide > div:last-child {
  padding-top: 16px;
}

@media screen and (max-width: 1199px) {
  .blog .slick-initialized.slick-slider .slick-slide > div:last-child {
    padding-top: 8px;
  }
}

.blog .slick-initialized.slick-slider .slick-slide > div:first-child {
  padding-bottom: 16px;
}

@media screen and (max-width: 1199px) {
  .blog .slick-initialized.slick-slider .slick-slide > div:first-child {
    padding-bottom: 8px;
  }
}

.blog .slick-next {
  right: -80px;
}

@media screen and (max-width: 1599px) {
  .blog .slick-next {
    right: -70px;
  }
}

@media screen and (max-width: 1499px) {
  .blog .slick-next {
    right: -20px;
  }
}

.blog .slick-prev {
  left: -80px;
}

@media screen and (max-width: 1599px) {
  .blog .slick-prev {
    left: -70px;
  }
}

@media screen and (max-width: 1499px) {
  .blog .slick-prev {
    left: -20px;
  }
}

.blog-item {
  width: calc(50% - 16px);
  border-radius: 16px;
  overflow: hidden;
  background-color: hsl(var(--white));
  border: 1px solid hsl(var(--base) / 0.2);
  display: block !important;
}

.blog-item__wrapper {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: stretch;
  -ms-flex-align: stretch;
  align-items: stretch;
  -webkit-box-pack: start;
  -ms-flex-pack: start;
  justify-content: flex-start;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
}

@media screen and (max-width: 767px) {
  .blog-item__wrapper {
    -webkit-box-orient: vertical;
    -webkit-box-direction: normal;
    -ms-flex-direction: column;
    flex-direction: column;
  }
}

.blog-item:hover .blog-item__thumb img {
  -webkit-transform: scale(1.1);
  transform: scale(1.1);
}

.blog-item:hover .border-effect {
  color: hsl(var(--base));
}

.blog-item__thumb {
  width: calc(50% - 24px);
  overflow: hidden;
}

@media screen and (max-width: 767px) {
  .blog-item__thumb {
    width: 100%;
  }
}

.blog-item__thumb img {
  -webkit-transition: 0.2s linear;
  transition: 0.2s linear;
}

.blog-item__content {
  width: calc(50% + 24px);
  padding: 32px 24px;
}

@media screen and (max-width: 767px) {
  .blog-item__content {
    width: 100%;
  }
}

@media screen and (max-width: 1199px) {
  .blog-item__content {
    padding: 20px 16px;
  }
}

@media screen and (max-width: 991px) {
  .blog-item__content {
    padding: 16px 12px;
  }
}

.blog-item__title {
  margin-bottom: 8px;
}

.blog-item__desc {
  margin-bottom: 24px;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  color: hsl(var(--body-color));
}

@media screen and (max-width: 1199px) {
  .blog-item__desc {
    margin-bottom: 16px;
  }
}

@media screen and (max-width: 991px) {
  .blog-item__desc {
    font-size: 14px;
    margin-bottom: 12px;
  }
}

.border-effect {
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
}

.blog-auth {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: start;
  -ms-flex-pack: start;
  justify-content: flex-start;
  gap: 16px;
}

@media screen and (max-width: 991px) {
  .blog-auth {
    gap: 8px;
  }
}

.blog-auth__thumb {
  -ms-flex-negative: 0;
  flex-shrink: 0;
}

.blog-auth__thumb img {
  height: 48px;
  width: 48px;
  -o-object-fit: cover;
  object-fit: cover;
}

@media screen and (max-width: 991px) {
  .blog-auth__thumb img {
    height: 32px;
    width: 32px;
  }
}

.blog-auth__content {
  -webkit-box-flex: 1;
  -ms-flex: 1;
  flex: 1;
}

.blog-auth__name {
  font-size: 14px;
  color: hsl(var(--base-two));
  line-height: 1.5;
  font-weight: 600;
}

.blog-publish {
  color: hsl(var(--body-color));
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  font-size: 14px;
  gap: 6px;
}

@media screen and (max-width: 991px) {
  .blog-publish {
    font-size: 12px;
    font-weight: 600;
    gap: 3px;
  }
}

.blog-publish__dot {
  font-size: 18px;
}

.brand-section__slider {
  background-color: hsl(var(--white));
  padding: 50px;
  border-radius: 24px;
  border: 1px solid hsl(var(--base) / 0.2);
}

.brand-slider-item {
  height: 100%;
  display: -webkit-box !important;
  display: -ms-flexbox !important;
  display: flex !important;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  justify-content: center;
  padding-inline: 12px;
}

.brand-slider-item img {
  max-height: 85px;
}

@media screen and (max-width: 575px) {
  .brand-slider-item img {
    max-height: 60px;
  }
}

.faq-wrapper .faq--accordion .accordion-item {
  border: 0;
}

.faq-wrapper .faq--accordion .accordion-item:not(:last-child) {
  margin-bottom: 22px;
}

@media screen and (max-width: 575px) {
  .faq-wrapper .faq--accordion .accordion-item:not(:last-child) {
    margin-bottom: 16px;
  }
}

.faq-wrapper .faq--accordion .accordion-button {
  background-color: hsl(var(--section-bg));
  font-size: 1rem;
  font-weight: 600;
  padding: 0;
  padding-bottom: 16px;
  border-bottom: 1px solid hsl(var(--base-two) / 0.2);
  border-radius: 0 !important;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: justify;
  -ms-flex-pack: justify;
  justify-content: space-between;
  -ms-flex-wrap: nowrap;
  flex-wrap: nowrap;
}

@media screen and (max-width: 575px) {
  .faq-wrapper .faq--accordion .accordion-button {
    font-size: 14px;
    padding-bottom: 12px;
  }
}

.faq-wrapper .faq--accordion .accordion-button .accordion-icon {
  height: 30px;
  width: 30px;
  display: grid;
  place-content: center;
  border-radius: 6px;
  color: hsl(var(--base-two));
  background: var(--gradient-color);
  font-size: 0.875rem;
}

@media screen and (max-width: 575px) {
  .faq-wrapper .faq--accordion .accordion-button .accordion-icon {
    height: 26px;
    width: 26px;
    font-size: 0.75rem;
  }
}

.faq-wrapper .faq--accordion .accordion-button .accordion-icon i {
  -webkit-transition: all linear 0.3s;
  transition: all linear 0.3s;
}

.faq-wrapper .faq--accordion .accordion-button:focus {
  -webkit-box-shadow: none;
  box-shadow: none;
}

.faq-wrapper .faq--accordion .accordion-button:not(.collapsed) {
  color: hsl(var(--base-two));
  background-color: hsl(var(--section-bg)) !important;
  -webkit-box-shadow: none !important;
  box-shadow: none !important;
}

.faq-wrapper .faq--accordion .accordion-button:not(.collapsed)::after {
  background-image: none;
}

.faq-wrapper .faq--accordion .accordion-button[aria-expanded="true"]::after,
.faq-wrapper .faq--accordion .accordion-button[aria-expanded="false"]::after {
  display: none;
}

.faq-wrapper
  .faq--accordion
  .accordion-button[aria-expanded="true"]
  .accordion-icon
  i,
.faq-wrapper
  .faq--accordion
  .accordion-button[aria-expanded="false"]
  .accordion-icon
  i {
  -webkit-transform: rotate(180deg);
  transform: rotate(180deg);
}

.faq-wrapper
  .faq--accordion
  .accordion-button[aria-expanded="false"]
  .accordion-icon
  i {
  -webkit-transform: rotate(0);
  transform: rotate(0);
}

.faq-wrapper .faq--accordion .accordion-body {
  padding: 16px 0;
  color: hsl(var(--body-color));
  background-color: hsl(var(--section-bg));
}

@media screen and (max-width: 575px) {
  .faq-wrapper .faq--accordion .accordion-body {
    padding: 12px 0;
    font-size: 14px;
  }
}

.about-section__tumb {
  overflow: hidden;
  border-radius: 24px;
}

.about-content__subtitle {
  color: hsl(var(--base));
  font-weight: 500;
  margin-bottom: 16px;
  font-size: 32px;
  line-height: 1;
}

@media screen and (max-width: 1399px) {
  .about-content__subtitle {
    font-size: 24px;
    margin-bottom: 12px;
  }
}

.about-content__title {
  margin-bottom: 24px;
}

@media screen and (max-width: 1399px) {
  .about-content__title {
    margin-bottom: 16px;
  }
}

.about-content__desc {
  font-size: 20px;
  line-height: 1.5;
  font-weight: 500;
  margin-bottom: 30px;
}

@media screen and (max-width: 1399px) {
  .about-content__desc {
    font-size: 18px;
  }
}

@media screen and (max-width: 1199px) {
  .about-content__desc {
    font-size: 16px;
    margin-bottom: 16px;
  }
}

.about-counter {
  padding: 24px 36px;
  background-color: hsl(var(--base));
  border-radius: 16px;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
  width: 896px;
  position: relative;
  right: 41%;
  gap: 24px 16px;
}

@media screen and (max-width: 1399px) {
  .about-counter {
    right: 0;
    width: 100%;
    padding: 20px;
  }
}

@media screen and (max-width: 575px) {
  .about-counter {
    gap: 16px 6px;
  }
}

.about-counter-item {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  gap: 24px;
  -webkit-box-flex: 1;
  -ms-flex: 1 1 auto;
  flex: 1 1 auto;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  justify-content: center;
}

@media screen and (max-width: 1399px) {
  .about-counter-item {
    gap: 12px;
  }
}

.about-counter-item:not(:last-child) {
  margin-right: 24px;
  padding-right: 24px;
  border-right: 2px solid hsl(var(--white));
}

@media screen and (max-width: 1399px) {
  .about-counter-item:not(:last-child) {
    margin-right: 12px;
    padding-right: 12px;
    border-right: 1px solid hsl(var(--white));
  }
}

@media screen and (max-width: 1199px) {
  .about-counter-item:not(:last-child) {
    margin-right: 6px;
    padding-right: 6px;
  }
}

.about-counter-item__icon {
  font-size: 64px;
  color: hsl(var(--white));
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
}

@media screen and (max-width: 1399px) {
  .about-counter-item__icon {
    font-size: 32px;
  }
}

@media screen and (max-width: 1199px) {
  .about-counter-item__icon {
    font-size: 24px;
  }
}

.about-counter-item__content {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  gap: 12px;
}

@media screen and (max-width: 1399px) {
  .about-counter-item__content {
    gap: 6px;
  }
}

.about-counter-item__count {
  margin-bottom: 0;
  font-weight: 800;
  color: hsl(var(--white));
}

@media screen and (max-width: 1399px) {
  .about-counter-item__count {
    font-size: 32px;
  }
}

@media screen and (max-width: 1199px) {
  .about-counter-item__count {
    font-size: 24px;
  }
}

.about-counter-item__title {
  font-size: 20px;
  color: hsl(var(--white));
  font-weight: 700;
  line-height: 1.2;
}

@media screen and (max-width: 1399px) {
  .about-counter-item__title {
    font-size: 14px;
  }
}

@media screen and (max-width: 1199px) {
  .about-counter-item__title {
    font-size: 12px;
    font-weight: 500;
  }
}

.support-section {
  background: var(--gradient-color);
  position: relative;
  z-index: 1;
}

.support-section:before {
  content: "";
  position: absolute;
  background-color: #000;
  opacity: 0.6;
  width: 100%;
  height: 100%;
  top: 0;
  left: 0;
  z-index: -1;
}

.support-section__title {
  color: hsl(var(--white));
  width: 100%;
  margin-bottom: 24px;
}

@media screen and (max-width: 991px) {
  .support-section__title {
    margin-bottom: 16px;
  }
}

.support-section__desc {
  color: hsl(var(--white));
  font-weight: 400;
  line-height: 1.5;
  font-size: 20px;
  margin-bottom: 32px;
}

@media screen and (max-width: 1199px) {
  .support-section__desc {
    font-size: 18px;
    margin-bottom: 24px;
  }
}

@media screen and (max-width: 991px) {
  .support-section__desc {
    margin-bottom: 16px;
  }
}

.support-section-thumb {
  border-radius: 12px;
  overflow: hidden;
}

.why-us-benifits {
  margin-bottom: 56px;
}

.why-us-benifits__list {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: start;
  -ms-flex-pack: start;
  justify-content: flex-start;
  gap: 24px 32px;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
}

@media screen and (max-width: 1399px) {
  .why-us-benifits__list {
    gap: 16px;
  }
}

@media screen and (max-width: 575px) {
  .why-us-benifits__list {
    gap: 12px;
  }
}

.why-us-benifits__item {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  gap: 14px;
  width: calc(33.3333333333% - 22px);
}

@media screen and (max-width: 1399px) {
  .why-us-benifits__item {
    width: calc(50% - 8px);
  }
}

@media screen and (max-width: 1399px) {
  .why-us-benifits__item {
    width: calc(33.3333333333% - 11px);
  }
}

@media screen and (max-width: 1399px) {
  .why-us-benifits__item {
    width: calc(50% - 8px);
  }
}

@media screen and (max-width: 575px) {
  .why-us-benifits__item {
    width: calc(50% - 6px);
    gap: 6px;
  }
}

.why-us-benifits__icon {
  font-size: 20px;
  background: var(--gradient-color);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
}

@media screen and (max-width: 575px) {
  .why-us-benifits__icon {
    font-size: 16px;
  }
}

@media screen and (max-width: 424px) {
  .why-us-benifits__icon {
    font-size: 14px;
  }
}

.why-us-benifits__text {
  font-weight: 600;
  list-style: 1;
  color: hsl(var(--base-two));
}

@media screen and (max-width: 575px) {
  .why-us-benifits__text {
    font-size: 14px;
  }
}

@media screen and (max-width: 424px) {
  .why-us-benifits__text {
    font-size: 13px;
  }
}

.why-us-thumb {
  padding: 50px 40px;
  border-radius: 24px;
  background-color: hsl(var(--base) / 0.15);
  position: relative;
  right: -140px;
}

@media screen and (max-width: 1599px) {
  .why-us-thumb {
    right: 0;
  }
}

@media screen and (max-width: 575px) {
  .why-us-thumb {
    padding: 32px;
  }
}

@media screen and (max-width: 424px) {
  .why-us-thumb {
    padding: 16px;
  }
}

.filter-search {
  margin-bottom: 40px;
}

.filter-search__wrapper {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  gap: 8px;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
}

@media screen and (max-width: 767px) {
  .filter-search__wrapper {
    gap: 12px;
  }
}

.filter-search__item {
  width: calc(20% - 7px);
}

@media screen and (max-width: 1199px) {
  .filter-search__item {
    width: calc(33.3333333333% - 6px);
  }
}

@media screen and (max-width: 767px) {
  .filter-search__item {
    width: calc(50% - 6px);
  }

  .filter-search__item:last-child {
    width: 100%;
  }
}

@media screen and (max-width: 424px) {
  .filter-search__item {
    width: 100%;
  }
}

.product-card {
  height: 100%;
  border-radius: 8px;
  overflow: hidden;
  background: hsl(var(--white));
  -webkit-box-shadow: 0px 2px 4px 0px hsl(var(--black) / 0.1);
  box-shadow: 0px 2px 4px 0px hsl(var(--black) / 0.1);
}

.product-card__body {
  padding: 24px 32px;
}

@media screen and (max-width: 1399px) {
  .product-card__body {
    padding: 16px;
  }
}

@media screen and (max-width: 575px) {
  .product-card__body {
    padding: 12px;
  }
}

.product-card__title {
  margin-bottom: 24px;
}

@media screen and (max-width: 1199px) {
  .product-card__title {
    margin-bottom: 16px;
  }
}

@media screen and (max-width: 767px) {
  .product-card__title {
    margin-bottom: 12px;
  }
}

.product-card-list {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  gap: 12px 24px;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
  margin-bottom: 16px;
  padding-bottom: 16px;
  border-bottom: 1px solid hsl(var(--base-two) / 0.1);
}

@media screen and (max-width: 1199px) {
  .product-card-list {
    gap: 12px;
  }
}

@media screen and (max-width: 767px) {
  .product-card-list {
    gap: 10px;
  }
}

@media screen and (max-width: 575px) {
  .product-card-list {
    margin-bottom: 12px;
    padding-bottom: 12px;
  }
}

.product-card-list__item {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  gap: 8px;
}

@media screen and (max-width: 1199px) {
  .product-card-list__item {
    gap: 4px;
  }
}

.product-card-list__icon {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  font-size: 16px;
}

@media screen and (max-width: 767px) {
  .product-card-list__icon {
    font-size: 13px;
  }
}

.product-card-list__text {
  font-size: 14px;
  font-weight: 700;
  line-height: 1;
}

@media screen and (max-width: 767px) {
  .product-card-list__text {
    font-size: 12px;
  }
}

.product-info {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: justify;
  -ms-flex-pack: justify;
  justify-content: space-between;
  gap: 12px;
}

@media screen and (max-width: 767px) {
  .product-info {
    -webkit-box-orient: vertical;
    -webkit-box-direction: normal;
    -ms-flex-direction: column;
    flex-direction: column;
    -webkit-box-align: start;
    -ms-flex-align: start;
    align-items: flex-start;
  }
}

.product-info__wrapper {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: start;
  -ms-flex-align: start;
  align-items: flex-start;
  gap: 8px;
}

.product-info__icon {
  font-size: 20px;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
}

.product-info__title {
  font-size: 14px;
  color: hsl(var(--base-two));
  display: block;
  line-height: 1;
  margin-bottom: 6px;
}

.product-info__price {
  color: hsl(var(--base-two));
  font-weight: 700;
  line-height: 1.2;
}

@media screen and (max-width: 1199px) {
  .product-info__price {
    font-size: 14px;
  }
}

.product-info__vat {
  font-size: 12px;
  font-weight: 600;
  display: block;
}

.product-info__btn {
  -ms-flex-negative: 0;
  flex-shrink: 0;
}

@media screen and (max-width: 767px) {
  .product-info__btn {
    width: 100%;
  }
}

.product-slider {
  position: relative;
}

.product-slider .slick-initialized.slick-slider {
  margin: 0 0;
}

.product-slider .slick-item {
  height: 100%;
  width: 100%;
  display: -webkit-box !important;
  display: -ms-flexbox !important;
  display: flex !important;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  justify-content: center;
  border-radius: 12px;
  overflow: hidden;
}

.product-slider .slick-arrow {
  color: hsl(var(--base-two) / 0.5);
  width: 56px;
  height: 56px;
  background-color: hsl(var(--white));
  border: 1px solid hsl(var(--base) / 0.3);
  font-size: 18px;
  -webkit-filter: drop-shadow(0px 2.986px 2.986px hsl(var(--black) / 0.1));
  filter: drop-shadow(0px 2.986px 2.986px hsl(var(--black) / 0.1));
}

@media screen and (max-width: 1399px) {
  .product-slider .slick-arrow {
    width: 50px;
    height: 50px;
    font-size: 16px;
  }
}

@media screen and (max-width: 991px) {
  .product-slider .slick-arrow {
    width: 40px;
    height: 40px;
    font-size: 16px;
  }
}

.product-slider .slick-arrow:hover {
  background-color: hsl(var(--base));
  color: hsl(var(--white));
}

.product-slider .slick-dots li button {
  background: hsl(var(--white));
  margin: 4px;
  width: 85px;
  height: 54px;
  border-radius: 8px;
  text-indent: unset;
  overflow: hidden;
}

.product-slider .slick-dots li button img {
  width: 100%;
}

@media screen and (max-width: 1199px) {
  .product-slider .slick-dots li button {
    width: 70px;
    height: 46px;
    border-radius: 6px;
  }

  .product-slider .slick-dots li button img {
    width: 100%;
  }
}

@media screen and (max-width: 575px) {
  .product-slider .slick-dots li button {
    width: 50px;
    height: 36px;
    margin: 2px;
  }

  .product-slider .slick-dots li button img {
    width: 100%;
  }
}

@media screen and (max-width: 424px) {
  .product-slider .slick-dots li button {
    width: 40px;
    height: 30px;
    margin: 2px;
  }

  .product-slider .slick-dots li button img {
    width: 100%;
  }
}

.product-slider .slick-dots li.slick-active button {
  -webkit-filter: opacity(0.5);
  filter: opacity(0.5);
}

.details-info-item__title {
  color: hsl(var(--base-two) / 0.6);
  font-size: 14px;
  font-weight: 600;
  line-height: 1;
  margin-bottom: 8px;
}

.details-info-item__content {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: start;
  -ms-flex-pack: start;
  justify-content: flex-start;
  gap: 16px;
  padding: 12px 16px;
  background-color: hsl(var(--white));
  border-radius: 4px;
  border: 1px solid hsl(var(--base) / 0.1);
}

@media screen and (max-width: 575px) {
  .details-info-item__content {
    padding: 9px 12px;
    gap: 8px;
  }
}

.details-info-item__icon {
  background: var(--gradient-color);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  font-size: 24px;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
}

@media screen and (max-width: 575px) {
  .details-info-item__icon {
    font-size: 18px;
  }
}

.details-info-item__desc {
  font-size: 14px;
  color: hsl(var(--base-two));
  font-weight: 700;
}

.details-info {
  padding: 16px;
  border-radius: 4px;
  border-radius: 4px;
  border: 1px solid hsl(var(--base-two) / 0.05);
  background: hsl(var(--base) / 0.1);
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: start;
  -ms-flex-align: start;
  align-items: flex-start;
  -webkit-box-pack: justify;
  -ms-flex-pack: justify;
  justify-content: space-between;
}

.details-info__left {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  gap: 8px;
}

.details-info__left .icon {
  background: var(--gradient-color);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  font-size: 20px;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
}

.details-info__left .text {
  font-size: 14px;
  font-weight: 500;
  color: hsl(var(--base-two));
}

.details-info-content__top .text {
  font-size: 12px;
  font-weight: 700;
}

.details-info-content__top .time {
  color: hsl(var(--white));
  font-size: 12px;
  font-weight: 600;
  background: var(--gradient-color);
  padding: 2px 8px;
  border-radius: 4px;
  line-height: 1.2;
}

.details-info-content__price {
  margin: 4px 0;
}

.details-info-content__vat {
  font-size: 12px;
  font-weight: 700;
}

.reserve-note-text {
  margin-top: 10px;
  text-align: center;
  font-weight: 600;
}

@media screen and (max-width: 575px) {
  .reserve-note-text {
    font-size: 14px;
  }
}

.tab-rental-desc .text {
  line-height: 1.5;
}

.tab-rental-desc .text:not(:last-child) {
  margin-bottom: 1rem;
}

.tab-rental-wrapper {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: stretch;
  -ms-flex-align: stretch;
  align-items: stretch;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
  gap: 20px;
  max-width: 540px;
  width: 100%;
}

@media screen and (max-width: 767px) {
  .tab-rental-wrapper {
    gap: 12px;
  }
}

.tab-rental-card {
  padding: 10px 20px;
  border-radius: 8px;
  border: 1px solid hsl(var(--base-two) / 0.1);
  gap: 12px;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: start;
  -ms-flex-pack: start;
  justify-content: flex-start;
  width: calc(50% - 10px);
}

@media screen and (max-width: 767px) {
  .tab-rental-card {
    width: calc(50% - 6px);
  }
}

@media screen and (max-width: 575px) {
  .tab-rental-card {
    width: 100%;
  }
}

.tab-rental-card .icon {
  font-size: 20px;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
}

.tab-rental-card .text {
  font-size: 18px;
  color: hsl(var(--base-two));
  font-weight: 500;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
}

@media screen and (max-width: 767px) {
  .tab-rental-card .text {
    font-size: 14px;
  }
}

.how-rental__title {
  margin-bottom: 16px;
}

.how-rental__wrapper {
  border-radius: 8px;
  border: 1px solid hsl(var(--base-two) / 0.1);
  padding: 32px 24px;
}

@media screen and (max-width: 767px) {
  .how-rental__wrapper {
    padding: 16px;
  }
}

.how-rental-item {
  text-align: center;
}

.how-rental-item__icon {
  font-size: 26px;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  margin-bottom: 16px;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  justify-content: center;
}

@media screen and (max-width: 575px) {
  .how-rental-item__icon {
    font-size: 20px;
  }
}

.how-rental-item__title {
  color: hsl(var(--base-two));
  font-size: 16px;
  font-weight: 700;
  line-height: 1.2;
  margin-bottom: 8px;
}

@media screen and (max-width: 575px) {
  .how-rental-item__title {
    font-size: 14px;
    margin-bottom: 4px;
  }
}

.how-rental-item__desc {
  line-height: 1.5;
  font-size: 14px;
  font-weight: 500;
}

@media screen and (max-width: 575px) {
  .how-rental-item__desc {
    font-size: 12px;
    font-weight: 600;
  }
}

.preview-video__title {
  margin-bottom: 16px;
}

.preview-video__thumb {
  border-radius: 12px;
  overflow: hidden;
  position: relative;
}

.preview-video__play {
  position: absolute;
  top: 50%;
  left: 50%;
  -webkit-transform: translate(-50%, -50%);
  transform: translate(-50%, -50%);
  background-color: hsl(var(--white));
  height: 80px;
  width: 80px;
  border-radius: 50%;
  display: grid;
  place-content: center;
}

.preview-video__play i {
  background: var(--gradient-color);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  font-size: 32px;
}

.benefits-tab-wrapper {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: stretch;
  -ms-flex-align: stretch;
  align-items: stretch;
  gap: 24px;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
}

@media screen and (max-width: 767px) {
  .benefits-tab-wrapper {
    gap: 16px;
  }
}

@media screen and (max-width: 575px) {
  .benefits-tab-wrapper {
    gap: 12px;
  }
}

.benefits-tab-item {
  padding: 16px 26px;
  border: 1px solid hsl(var(--base-two) / 0.1);
  border-radius: 8px;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  justify-content: center;
  gap: 12px;
}

@media screen and (max-width: 767px) {
  .benefits-tab-item {
    padding: 12px 16px;
  }
}

.benefits-tab-item .icon {
  background: var(--gradient-color);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  font-size: 24px;
  line-height: 1;
}

@media screen and (max-width: 1199px) {
  .benefits-tab-item .icon {
    font-size: 18px;
  }
}

.benefits-tab-item .text {
  font-size: 20px;
  font-weight: 500;
  color: hsl(var(--base-two));
  line-height: 1;
}

@media screen and (max-width: 1199px) {
  .benefits-tab-item .text {
    font-size: 16px;
  }
}

.review-item {
  padding: 16px 24px;
  border-radius: 8px;
  border: 1px solid hsl(var(--base-two) / 0.1);
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
  -ms-flex-direction: column;
  flex-direction: column;
  gap: 24px;
  margin-right: 60px;
}

@media screen and (max-width: 1199px) {
  .review-item {
    gap: 16px;
  }
}

@media screen and (max-width: 767px) {
  .review-item {
    margin-right: 30px;
    gap: 12px;
    padding: 16px;
  }
}

.review-item:not(:last-child) {
  margin-bottom: 24px;
}

.review-item.reply {
  margin-right: 0;
  margin-left: 60px;
  background-color: hsl(var(--white));
}

@media screen and (max-width: 767px) {
  .review-item.reply {
    margin-left: 30px;
  }
}

.review-item__desc {
  line-height: 1.5;
  color: hsl(var(--base-two));
}

@media screen and (max-width: 575px) {
  .review-item__desc {
    font-size: 14px;
  }
}

.review-item-auth {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  gap: 20px;
}

@media screen and (max-width: 575px) {
  .review-item-auth {
    gap: 12px;
  }
}

.review-item-auth__thumb {
  -ms-flex-negative: 0;
  flex-shrink: 0;
}

.review-item-auth__thumb img {
  height: 56px;
  width: 56px;
  -o-object-fit: cover;
  object-fit: cover;
  border-radius: 50%;
}

@media screen and (max-width: 575px) {
  .review-item-auth__thumb img {
    width: 32px;
    height: 32px;
  }
}

.review-item-auth__info {
  -webkit-box-flex: 1;
  -ms-flex: 1;
  flex: 1;
}

.review-item-auth__name {
  color: hsl(var(--base-two));
  font-weight: 600;
  line-height: 1.5;
}

@media screen and (max-width: 575px) {
  .review-item-auth__name {
    font-size: 14px;
  }
}

.review-item-auth__designation {
  line-height: 1.5;
  font-size: 14px;
  font-weight: 500;
}

@media screen and (max-width: 575px) {
  .review-item-auth__designation {
    font-size: 12px;
  }
}

.rating_wrapper .rating-group {
  display: -webkit-inline-box;
  display: -ms-inline-flexbox;
  display: inline-flex;
}

.rating_wrapper .rating__input {
  position: absolute !important;
  left: -9999px !important;
}

.rating_wrapper .rating__label {
  cursor: pointer;
  padding: 0 0.1em;
  font-size: 20px;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
}

.rating_wrapper .rating__icon--star {
  color: hsl(var(--base));
}

.rating_wrapper .rating__input:checked ~ .rating__label .rating__icon--star {
  color: hsl(var(--base-two) / 0.1);
}

.rating_wrapper .rating-group:hover .rating__label .rating__icon--star {
  color: hsl(var(--base));
}

.rating_wrapper .rating__input:hover ~ .rating__label .rating__icon--star {
  color: hsl(var(--base-two) / 0.1);
}

.review-form__title {
  margin-bottom: 12px;
  font-weight: 700;
}

.review-form__desc {
  color: hsl(var(--base-two));
  line-height: 1.5;
}

.review-form__top {
  margin-bottom: 40px;
}

.review-form-rating {
  margin-bottom: 24px;
}

.review-form-rating__label {
  font-weight: 700;
  color: hsl(var(--base-two));
  margin-bottom: 8px;
}

.details-faq-content {
  color: hsl(var(--base-two) / 0.6);
}

.details-faq-content ol {
  list-style: decimal;
  padding-left: 20px;
}

/* =========================================== Blog Details Css Start ==============================*/
.blog-details {
  overflow: hidden;
  height: 100%;
  -webkit-box-shadow: 0px 2px 15px hsl(var(--white) / 0.25);
  box-shadow: 0px 2px 15px hsl(var(--white) / 0.25);
}

.blog-details__thumb {
  height: 450px;
  max-height: 100%;
}

@media screen and (max-width: 991px) {
  .blog-details__thumb {
    height: 350px;
  }
}

@media screen and (max-width: 767px) {
  .blog-details__thumb {
    height: 300px;
  }
}

.blog-details__thumb img {
  border-radius: 8px;
}

.blog-details__title {
  margin-top: 20px;
  margin-bottom: 15px;
}

.blog-details__desc {
  margin-bottom: 15px;
}

.blog-details .social-list__link {
  border: 1px solid hsl(var(--black) / 0.1);
  color: hsl(var(--text-color));
}

.blog-details .social-list__link:hover {
  border-color: hsl(var(--base));
  color: hsl(var(--white));
}

.quote-text {
  background-color: hsl(var(--base) / 0.1);
  padding: 30px 20px;
  border-radius: 5px;
  border-left: 3px solid hsl(var(--base));
  margin-bottom: 20px;
}

@media screen and (max-width: 767px) {
  .quote-text {
    padding: 25px 15px;
  }
}

/* ========================================== Blog Details Css End ======================================*/
/* =========================================== Account Css Start =========================*/
.account-inner {
  position: relative;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: stretch;
  -ms-flex-align: stretch;
  align-items: stretch;
  -webkit-box-pack: justify;
  -ms-flex-pack: justify;
  justify-content: space-between;
  min-height: 100vh;
}

.account-inner .custom--tab {
  border: 0;
}

.account-inner .form--control {
  border: 1px solid hsl(var(--base) / 0.2);
}

.account-back {
  position: fixed;
  top: 20px;
  right: 20px;
  cursor: pointer;
  height: 40px;
  width: 40px;
  background-color: hsl(var(--white));
  border: 1px solid hsl(var(--base));
  display: grid;
  place-content: center;
  font-size: 24px;
  color: hsl(var(--base));
  border-radius: 6px;
  z-index: 999;
}

@media screen and (max-width: 575px) {
  .account-back {
    height: 32px;
    width: 32px;
    font-size: 18px;
  }
}

.auth--btn-wrapper {
  display: flex;
  align-items: center;
  justify-content: flex-start;
  gap: 12px;
  margin-top: 16px;
  flex-wrap: wrap;
}

.auth--btn-inner {
  flex: 1 1 auto;
}

.auth--btn {
  padding: 9px 16px;
  border: 1px solid hsl(var(--base) / 0.2);
  display: -webkit-box !important;
  display: -ms-flexbox !important;
  display: flex !important;
  align-items: center;
  justify-content: flex-start;
  gap: 8px;
}

.auth--btn:hover,
.auth--btn:focus {
  border: 1px solid hsl(var(--base) / 0.2);
}

.auth--btn .icon {
  max-width: 20px;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  margin: 0;
}

.account-tab {
  max-width: 720px;
  width: 100%;
  background-color: hsl(var(--white));
  display: flex;
  align-items: center;
  justify-content: center;
  padding-block: 40px;
  flex-direction: column;
  gap: 30px;
}

.auth-devide {
  text-align: center;
  margin-block: 32px;
  position: relative;
  z-index: 1;

  @media (max-width: 1199px) {
    margin-block: 24px;
  }
}

.auth-devide::after {
  content: "";
  position: absolute;
  height: 1px;
  width: 100%;
  background-color: hsl(var(--black) / 0.1);
  top: 50%;
  left: 0;
  z-index: -1;
}

.auth-devide span {
  font-weight: 500;
  padding-inline: 6px;
  background-color: hsl(var(--white));
}

.account-logo {
  max-width: 165px;
}

@media screen and (max-width: 1399px) {
  .account-tab {
    max-width: 600px;
  }
}

@media screen and (max-width: 1199px) {
  .account-tab {
    max-width: 500px;
  }
}

@media screen and (max-width: 991px) {
  .account-tab {
    max-width: 420px;
    padding-inline: 15px;
  }
}

@media screen and (max-width: 767px) {
  .account-tab {
    max-width: 100%;
  }
}

.account-tab__inner {
  max-width: 480px;
  width: 100%;
  margin: 0 auto;
}

@media screen and (max-width: 1199px) {
  .account-tab__inner {
    max-width: 450px;
  }
}

.account-tab__inner .nav-item {
  width: 50%;
}

.account-tab__inner .nav-item .nav-link {
  width: 100%;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  justify-content: center;
}

.account-form {
  border-radius: 10px;
}

.account-form__content {
  text-align: center;
}

.account-thumb {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  justify-content: center;
  -webkit-box-flex: 1;
  -ms-flex: 1;
  flex: 1;
}

.account-thumb img {
  max-width: 740px;
  width: 100%;
}

@media screen and (max-width: 767px) {
  .account-thumb {
    display: none;
  }
}

/* ============================================ Account Css End ====================================*/
/*# sourceMappingURL=main.css.map */

.sidebar-submenu {
  display: none;
}

.sidebar-submenu.open-submenu {
  display: block;
}

.sidebar-submenu-list {
  padding: 0;
  background: hsl(var(--black) / 0.05);
  border-radius: 6px;
  margin-top: 6px;
}

.sidebar-submenu-list__link:has(.sidebar-submenu-list__badge) {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.sidebar-submenu-list__badge {
  background-color: hsl(var(--base));
  color: hsl(var(--white));
  border-radius: 50%;
  width: 16px;
  height: 16px;
  font-size: 10px;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 2px;
}

.sidebar-submenu-list__item.active > a {
  color: #000;
  background-color: #000;
}

.sidebar-submenu-list__link {
  padding: 8px 16px;
  padding-left: 25px;
  width: 100%;
  color: hsl(var(--base-two));
  font-weight: 600;
  border-radius: 8px;
  line-height: 1.5;
  font-size: 15px;
}

.sidebar-submenu-list__link:hover {
  background-color: hsl(var(--base) / 0.1);
}

.sidebar-submenu-list__link.active {
  background-color: hsl(var(--base) / 0.1);
  color: hsl(var(--base));
}

.sidebar-submenu-list__link .icon {
  margin-right: 8px;
  text-align: center;
  border-radius: 4px;
}

.has-dropdown .sidebar-menu-list__link {
  position: relative;
}

.has-dropdown .sidebar-menu-list__link::after {
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  right: 12px;
  color: hsl(var(--base-two));
  content: "\f078";
  font-family: "Font Awesome 5 Free";
  font-weight: 900;
  transition: all linear 0.3s;
  font-size: 14px;
}

.has-dropdown.active .sidebar-menu-list__link::after {
  transform: translateY(-50%) rotate(180deg);
}

.has-dropdown.active .sidebar-submenu {
  display: block;
}

/* ============================== store image ==================== */

#upload-img {
  border-radius: 8px;
  height: 150px;
}

.profile-thumb .thumb {
  position: relative;
}

.profile-thumb .thumb img {
  width: 100%;
}

.profile-thumb .update-thumb-icon {
  position: absolute;
  width: 35px;
  height: 35px;
  right: 0;
  bottom: 0;
  display: grid;
  place-items: center;
  font-size: 18px;
}

.badge.badge--icon {
  padding: 9px;
  border-radius: 8px 0 8px 0;
  font-size: 13px;
}

.badge--fill-base {
  background-color: hsl(var(--base));
  color: hsl(var(--white));
}

.store-alert-message i {
  color: hsl(var(--danger));
  font-size: 20px;
}

.store-alert-message p {
  font-size: 16px;
  font-weight: 600;
}

.table--form {
  width: 50%;
  margin: 20px 20px 0 0;
}

@media screen and (max-width: 767px) {
  .table--form {
    width: 100%;
    margin: 20px 20px 0 20px;
  }
}

.filter-search-form {
  max-width: 280px;
  width: 100%;
  margin-left: auto;
}

.table tbody tr:last-child td {
  border-bottom: 0;
}

.filter-btn-wrapper {
  display: flex;
  align-items: center;
  gap: 6px;
  flex-flow: wrap;
}

.button--group {
  display: flex;
  justify-content: end;
  flex-flow: wrap;
  gap: 6px;
}

.support-card {
  border: 1px solid hsl(var(--base) / 0.2);
  border-radius: 16px;
  background: hsl(var(--white));
  margin-bottom: 15px;
}

.support-card__title {
  margin-top: 0;
  margin-bottom: 0.5rem;
}

@media screen and (min-width: 768px) {
  .support-card__title {
    margin-bottom: 0;
  }
}

.support-card__head {
  padding: 12px 15px;
  border-bottom: 1px solid hsl(var(--base) / 0.2);
}

@media screen and (min-width: 768px) {
  .support-card__head {
    display: flex;
    justify-content: space-between;
  }
}

.support-card__date {
  display: block;
  line-height: 1;
}

.support-card__body {
  padding: 12px 15px;
}

.support-card__body-text {
  font-size: 14px;
  margin-bottom: 0;
  color: hsl(var(--dark) / 0.8);
}

.support-card__list {
  --gap: 0.5rem;
}

@media screen and (min-width: 768px) {
  .support-card__list {
    margin-top: 1rem;
  }
}

.support-card__file {
  display: flex;
  align-items: center;
  gap: 5px;
  padding: 7px 10px;
  border-radius: 5px;
  font-size: 14px;
  line-height: 1;
  color: hsl(var(--base));
}

.support-list {
  padding: 0;
}

/* =======================select2 ========================== */

.select2-container .select2-selection--single,
.select2-container--default
  .select2-selection--single
  .select2-selection__arrow {
  height: 47px !important;
  border-radius: 8px;
}

.select2-container--default .select2-selection--single,
.select2-container--default .select2-search--dropdown .select2-search__field {
  border: 1px solid hsl(var(--base-two) / 0.1);
  display: flex;
  align-items: center;
}

.select2-container--default
  .select2-selection--single
  .select2-selection__rendered {
  line-height: 47px !important;
}

input.select2-search__field {
  outline: none;
}

.select2-search--dropdown .select2-search__field {
  padding: 8px 4px;
}

span#select2-brand_id-15-container {
  outline: none;
}

.select2 .selection {
  width: 100%;
}

.select2-container--default .select2-selection--multiple {
  background-color: transparent;
  border: 1px solid #e0e0e0;
  line-height: 36px;
  border-radius: 8px;
  padding-left: 10px;
}

.select2-container--default.select2-container--focus
  .select2-selection--multiple {
  border: 1px solid hsl(var(--base-two) / 0.1) !important;
}

.select2-container--default
  .select2-results__option--highlighted.select2-results__option--selectable {
  background-color: hsl(var(--base));
}

.language {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  gap: 8px;
}

@media (max-width: 991px) {
  .language {
    margin-bottom: 12px;
  }

  .language .dropdown-menu {
    background-color: hsl(var(--white)) !important;
    border: 1px solid hsl(var(--base) / 0.2);
    top: calc(100% + 5px) !important;
  }
}

.language__icon {
  height: 30px;
  width: 30px;
  border-radius: 50%;
  background: var(--gradient-color);
  color: hsl(var(--black));
  display: grid;
  place-content: center;
  font-size: 14px;
}

.language__wrapper {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  gap: 8px;
  cursor: pointer;
}

.language__wrapper.show .language__arrow {
  -webkit-transform: rotate(180deg);
  transform: rotate(180deg);
}

.language__text {
  color: hsl(var(--base-two));
  font-size: 14px;
  font-weight: 500;
}

.language__arrow {
  font-size: 12px;
  color: hsl(var(--base-two));
  -webkit-transition: all linear 0.2s;
  transition: all linear 0.2s;
}

.language .dropdown-menu {
  position: absolute;
  -webkit-transition: ease-in-out 0.1s;
  transition: ease-in-out 0.1s;
  opacity: 0;
  visibility: hidden;
  top: 100%;
  display: unset;
  background: #fde4d4;
  -webkit-transform: scaleY(1);
  transform: scaleY(1);
  min-width: 150px;
  padding: 7px 0 !important;
  border-radius: 8px;
}

.language .dropdown-menu.show {
  visibility: visible;
  opacity: 1;
}

.language .dropdown-menu .language_text {
  color: hsl(var(--base-two));
  padding: 3px 16px;
  font-size: 14px;
  cursor: pointer;
  font-weight: 600;
}

.language .dropdown-menu .language_text:hover {
  background-color: hsl(var(--base));
  color: hsl(var(--black));
}

.blog-item {
  width: 100%;
  height: 100%;
}

.clt-section-wrapper {
  position: relative;
}

.clt-section-wrapper-video {
  position: absolute;
  height: 100%;
  width: 100%;
  top: 0;
  left: 0;
  z-index: -1;
}

.clt-section-wrapper-video video {
  height: 100%;
  width: 100%;
  object-fit: cover;
}

.h-50 {
  height: 50px !important;
}

.menu-count {
  font-size: 11px;
  color: hsl(var(--white));
  background: var(--gradient-color);
  padding: 5px 7px;
  line-height: 1;
  border-radius: 6px;
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  right: 32px;
}

.sidebar-menu-list__item.has-dropdown.active .menu-count {
  background: hsl(var(--base-two));
}

.rating .rating-form-group {
  position: relative;
  height: 24px;
  line-height: 24px;
  font-size: 24px;
  cursor: pointer;
}

.rating .rating-form-group .star-label {
  position: absolute;
  top: 0;
  left: 0;
  height: 100%;
  cursor: pointer;
}

.rating .rating-form-group .star-label:last-child {
  position: static;
}

.rating .rating-form-group .star-label:nth-child(1) {
  z-index: 5;
}

.rating .rating-form-group .star-label:nth-child(2) {
  z-index: 4;
}

.rating .rating-form-group .star-label:nth-child(3) {
  z-index: 3;
}

.rating .rating-form-group .star-label:nth-child(4) {
  z-index: 2;
}

.rating .rating-form-group .star-label:nth-child(5) {
  z-index: 1;
}

.rating .rating-form-group .star-label input {
  display: none;
}

.rating .rating-form-group .star-label .icon {
  float: left;
  color: transparent;
}

.rating .rating-form-group .star-label:last-child .icon {
  color: hsl(var(--dark) / 0.4);
}

.rating .rating-form-group:not(:hover) label input:checked ~ .icon,
.rating .rating-form-group:hover label:hover input ~ .icon {
  color: hsl(var(--warning));
}

.rating .rating-form-group label input:focus:not(:checked) ~ .icon:last-child {
  color: hsl(var(--white) / 0.1);
  text-shadow: 0 0 5px hsl(var(--warning));
}

/* ============404================== */
.error-content {
  position: relative;
  display: flex;
  flex-wrap: wrap;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  min-height: 100vh;
}

.error-content__inner {
  position: relative;
}

.error-content__title {
  font-size: 150px;
  line-height: 1;
  font-weight: 700;
  margin-bottom: 8px;
}

.error-content__subtitle {
  font-size: 36px;
  line-height: 1.3;
  margin-bottom: 8px;
}

.error-content__desc {
  font-size: clamp(1rem, 0.826rem + 0.485vw, 1.25rem);
  line-height: 26px;
  margin-bottom: 44px;
}

.error-content .icon {
  color: hsl(var(--base));
}

@media (max-width: 991px) {
  .error-content__title {
    font-size: 136px;
  }

  .error-content__subtitle {
    font-size: 32px;
  }

  .error-content__desc {
    margin-bottom: 30px;
  }
}

@media (max-width: 767px) {
  .error-content__title {
    font-size: 118px;
    margin-bottom: 5px;
  }

  .error-content__subtitle {
    font-size: 28px;
    margin-bottom: 5px;
  }

  .error-content__desc {
    margin-bottom: 25px;
  }
}

@media (max-width: 575px) {
  .error-content__title {
    font-size: 110px;
  }

  .error-content__subtitle {
    font-size: 25px;
  }

  .error-content__desc {
    margin-bottom: 20px;
  }
}

@media (max-width: 425px) {
  .error-content__title {
    font-size: 98px;
  }

  .error-content__subtitle {
    font-size: 22px;
  }

  .error-content__desc {
    margin-bottom: 15px;
  }
}
