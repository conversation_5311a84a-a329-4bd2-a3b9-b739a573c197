@font-face {
  font-family: 'icomoon';
  src: url('../fonts/icomoon.eot');
  src: url('../fonts/icomoon.eot') format('embedded-opentype'),
    url('../fonts/icomoon.ttf?cc5vwf') format('truetype'),
    url('../fonts/icomoon.woff?cc5vwf') format('woff'),
    url('../fonts/icomoon.svg?cc5vwf#icomoon') format('svg');
  font-weight: normal;
  font-style: normal;
  font-display: block;
}

[class^="icon-"],
[class*=" icon-"] {
  /* use !important to prevent issues with browser extensions that change fonts */
  font-family: 'icomoon' !important;
  speak: never;
  font-style: normal;
  font-weight: normal;
  font-variant: normal;
  text-transform: none;
  line-height: 1;

  /* Better Font Rendering =========== */
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.icon-Clip-path-group:before {
  content: "\e900";
}

.icon-Arrow-1-Stroke:before {
  content: "\e901";
}

.icon-Clip-path-group-1:before {
  content: "\e902";
}

.icon-Clip-path-group-2:before {
  content: "\e903";
}

.icon-Clip-path-group-3:before {
  content: "\e904";
}

.icon-Clip-path-group-4:before {
  content: "\e905";
}

.icon-Clip-path-group-5 .path1:before {
  content: "\e906";
  color: rgb(69, 69, 69);
}

.icon-Clip-path-group-5 .path2:before {
  content: "\e907";
  margin-left: -1em;
  color: rgb(244, 187, 25);
}

.icon-Clip-path-group-6:before {
  content: "\e908";
}

.icon-Email:before {
  content: "\e909";
}

.icon-Facebook:before {
  content: "\e90a";
}

.icon-fi_1437:before {
  content: "\e90b";
}

.icon-fi_483497:before {
  content: "\e90c";
}

.icon-fi_854952:before {
  content: "\e90d";
}

.icon-fi_3014621:before {
  content: "\e90e";
}

.icon-fi_3161830:before {
  content: "\e90f";
}

.icon-fi_3436734:before {
  content: "\e910";
}

.icon-fi_3534548:before {
  content: "\e911";
}

.icon-fi_3706334:before {
  content: "\e912";
}

.icon-fi_6686695:before {
  content: "\e913";
}

.icon-fi_8541356:before {
  content: "\e914";
}

.icon-fi_9156007:before {
  content: "\e915";
}

.icon-fi_9851490:before {
  content: "\e916";
}

.icon-fi_10156100:before {
  content: "\e917";
}

.icon-fi_14183674:before {
  content: "\e918";
}

.icon-Group-17:before {
  content: "\e919";
}

.icon-Group-18:before {
  content: "\e91a";
}

.icon-Group-20:before {
  content: "\e91b";
}

.icon-Group-31 .path1:before {
  content: "\e91c";
  color: rgb(255, 233, 181);
}

.icon-Group-31 .path2:before {
  content: "\e91d";
  margin-left: -1em;
  color: rgb(0, 0, 0);
}

.icon-Group-33 .path1:before {
  content: "\e91e";
  color: rgb(0, 0, 0);
}

.icon-Group-33 .path2:before {
  content: "\e91f";
  margin-left: -1em;
  color: rgb(0, 0, 0);
}

.icon-Group-33 .path3:before {
  content: "\e920";
  margin-left: -1em;
  color: rgb(247, 231, 186);
}

.icon-Group-33 .path4:before {
  content: "\e921";
  margin-left: -1em;
  color: rgb(254, 248, 231);
}

.icon-Group-33 .path5:before {
  content: "\e922";
  margin-left: -1em;
  color: rgb(255, 255, 255);
}

.icon-Group-33 .path6:before {
  content: "\e923";
  margin-left: -1em;
  color: rgb(0, 0, 0);
}

.icon-Group-33 .path7:before {
  content: "\e924";
  margin-left: -1em;
  color: rgb(255, 255, 255);
}

.icon-Group-33 .path8:before {
  content: "\e925";
  margin-left: -1em;
  color: rgb(0, 0, 0);
}

.icon-Group-33 .path9:before {
  content: "\e926";
  margin-left: -1em;
  color: rgb(255, 255, 255);
}

.icon-Group-33 .path10:before {
  content: "\e927";
  margin-left: -1em;
  color: rgb(0, 0, 0);
}

.icon-Group-33 .path11:before {
  content: "\e928";
  margin-left: -1em;
  color: rgb(0, 0, 0);
}

.icon-Group-33 .path12:before {
  content: "\e929";
  margin-left: -1em;
  color: rgb(255, 123, 74);
}

.icon-Group-33 .path13:before {
  content: "\e92a";
  margin-left: -1em;
  color: rgb(0, 0, 0);
}

.icon-Group-33 .path14:before {
  content: "\e92b";
  margin-left: -1em;
  color: rgb(0, 0, 0);
}

.icon-Group-33 .path15:before {
  content: "\e92c";
  margin-left: -1em;
  color: rgb(0, 0, 0);
}

.icon-Group-33 .path16:before {
  content: "\e92d";
  margin-left: -1em;
  color: rgb(0, 0, 0);
}

.icon-Group-33 .path17:before {
  content: "\e92e";
  margin-left: -1em;
  color: rgb(0, 0, 0);
}

.icon-Group-34 .path1:before {
  content: "\e92f";
  color: rgb(246, 218, 137);
}

.icon-Group-34 .path2:before {
  content: "\e930";
  margin-left: -1em;
  color: rgb(0, 0, 0);
}

.icon-Group-35 .path1:before {
  content: "\e931";
  color: rgb(246, 218, 137);
}

.icon-Group-35 .path2:before {
  content: "\e932";
  margin-left: -0.9580078125em;
  color: rgb(55, 54, 68);
}

.icon-Group-35 .path3:before {
  content: "\e933";
  margin-left: -0.9580078125em;
  color: rgb(55, 54, 68);
}

.icon-Group-35 .path4:before {
  content: "\e934";
  margin-left: -0.9580078125em;
  color: rgb(0, 0, 0);
}

.icon-Group-35 .path5:before {
  content: "\e935";
  margin-left: -0.9580078125em;
  color: rgb(255, 255, 255);
}

.icon-Group-35 .path6:before {
  content: "\e936";
  margin-left: -0.9580078125em;
  color: rgb(209, 162, 59);
}

.icon-Group-35 .path7:before {
  content: "\e937";
  margin-left: -0.9580078125em;
  color: rgb(40, 39, 51);
}

.icon-Group-35 .path8:before {
  content: "\e938";
  margin-left: -0.9580078125em;
  color: rgb(40, 39, 51);
}

.icon-Group-35 .path9:before {
  content: "\e939";
  margin-left: -0.9580078125em;
  color: rgb(255, 255, 255);
}

.icon-Group-35 .path10:before {
  content: "\e93a";
  margin-left: -0.9580078125em;
  color: rgb(255, 255, 255);
}

.icon-Group-35 .path11:before {
  content: "\e93b";
  margin-left: -0.9580078125em;
  color: rgb(255, 255, 255);
}

.icon-Group-36 .path1:before {
  content: "\e93c";
  color: rgb(0, 0, 0);
}

.icon-Group-36 .path2:before {
  content: "\e93d";
  margin-left: -0.875em;
  color: rgb(246, 218, 137);
}

.icon-Group-37 .path1:before {
  content: "\e93e";
  color: rgb(244, 187, 25);
}

.icon-Group-37 .path2:before {
  content: "\e93f";
  margin-left: -1em;
  color: rgb(244, 187, 25);
}

.icon-Group-37 .path3:before {
  content: "\e940";
  margin-left: -1em;
  color: rgb(69, 69, 69);
}

.icon-Group-37 .path4:before {
  content: "\e941";
  margin-left: -1em;
  color: rgb(69, 69, 69);
}

.icon-Group-38 .path1:before {
  content: "\e942";
  color: rgb(244, 187, 25);
}

.icon-Group-38 .path2:before {
  content: "\e943";
  margin-left: -1em;
  color: rgb(244, 187, 25);
}

.icon-Group-38 .path3:before {
  content: "\e944";
  margin-left: -1em;
  color: rgb(69, 69, 69);
}

.icon-Group-39 .path1:before {
  content: "\e945";
  color: rgb(0, 0, 0);
}

.icon-Group-39 .path2:before {
  content: "\e946";
  margin-left: -1em;
  color: rgb(50, 24, 7);
}

.icon-Group-40 .path1:before {
  content: "\e947";
  color: rgb(0, 0, 0);
}

.icon-Group-40 .path2:before {
  content: "\e948";
  margin-left: -1em;
  color: rgb(244, 187, 25);
  opacity: 0.7;
}

.icon-Group-40 .path3:before {
  content: "\e949";
  margin-left: -1em;
  color: rgb(205, 153, 3);
  opacity: 0.5;
}

.icon-Group-40 .path4:before {
  content: "\e94a";
  margin-left: -1em;
  color: rgb(205, 153, 3);
  opacity: 0.5;
}

.icon-Group-40 .path5:before {
  content: "\e94b";
  margin-left: -1em;
  color: rgb(248, 248, 248);
}

.icon-Group-40 .path6:before {
  content: "\e94c";
  margin-left: -1em;
  color: rgb(248, 248, 248);
}

.icon-Group-40 .path7:before {
  content: "\e94d";
  margin-left: -1em;
  color: rgb(248, 248, 248);
}

.icon-Group-41 .path1:before {
  content: "\e94e";
  color: rgb(246, 218, 137);
}

.icon-Group-41 .path2:before {
  content: "\e94f";
  margin-left: -1em;
  color: rgb(246, 218, 137);
}

.icon-Group-41 .path3:before {
  content: "\e950";
  margin-left: -1em;
  color: rgb(0, 0, 0);
}

.icon-Group-41 .path4:before {
  content: "\e951";
  margin-left: -1em;
  color: rgb(0, 0, 0);
}

.icon-Group-41 .path5:before {
  content: "\e952";
  margin-left: -1em;
  color: rgb(0, 0, 0);
}

.icon-Group-42 .path1:before {
  content: "\e953";
  color: rgb(0, 0, 0);
}

.icon-Group-42 .path2:before {
  content: "\e954";
  margin-left: -0.875em;
  color: rgb(244, 187, 25);
  opacity: 0.5;
}

.icon-Group-42 .path3:before {
  content: "\e955";
  margin-left: -0.875em;
  color: rgb(0, 0, 0);
}

.icon-Group-42 .path4:before {
  content: "\e956";
  margin-left: -0.875em;
  color: rgb(244, 187, 25);
  opacity: 0.5;
}

.icon-Group-42 .path5:before {
  content: "\e957";
  margin-left: -0.875em;
  color: rgb(0, 0, 0);
}

.icon-Group-60:before {
  content: "\e958";
}

.icon-Group-61 .path1:before {
  content: "\e959";
  color: rgb(0, 0, 0);
}

.icon-Group-61 .path2:before {
  content: "\e95a";
  margin-left: -1em;
  color: rgb(255, 233, 181);
}

.icon-Group-62 .path1:before {
  content: "\e95b";
  color: rgb(255, 233, 181);
}

.icon-Group-62 .path2:before {
  content: "\e95c";
  margin-left: -1em;
  color: rgb(0, 0, 0);
}

.icon-Group-62 .path3:before {
  content: "\e95d";
  margin-left: -1em;
  color: rgb(0, 0, 0);
}

.icon-Group-62 .path4:before {
  content: "\e95e";
  margin-left: -1em;
  color: rgb(0, 0, 0);
}

.icon-Group-62 .path5:before {
  content: "\e95f";
  margin-left: -1em;
  color: rgb(255, 233, 181);
}

.icon-Group:before {
  content: "\e960";
  color: #fd7923;
}

.icon-Instagram:before {
  content: "\e961";
}

.icon-Layer_1:before {
  content: "\e962";
}

.icon-LinkedIn:before {
  content: "\e963";
}

.icon-Live-Chat:before {
  content: "\e964";
}

.icon-Phone:before {
  content: "\e965";
}

.icon-Pin:before {
  content: "\e966";
}

.icon-svg5:before {
  content: "\e967";
}

.icon-svgexport-3 .path1:before {
  content: "\e968";
  color: rgb(0, 0, 0);
}

.icon-svgexport-3 .path2:before {
  content: "\e969";
  margin-left: -1em;
  color: rgb(255, 233, 181);
}

.icon-svgexport-3 .path3:before {
  content: "\e96a";
  margin-left: -1em;
  color: rgb(255, 233, 181);
}

.icon-svgexport-3 .path4:before {
  content: "\e96b";
  margin-left: -1em;
  color: rgb(255, 255, 255);
}

.icon-svgexport-3 .path5:before {
  content: "\e96c";
  margin-left: -1em;
  color: rgb(0, 0, 0);
}

.icon-svgexport-3 .path6:before {
  content: "\e96d";
  margin-left: -1em;
  color: rgb(255, 255, 255);
}

.icon-svgexport-3 .path7:before {
  content: "\e96e";
  margin-left: -1em;
  color: rgb(0, 0, 0);
}

.icon-svgexport-3 .path8:before {
  content: "\e96f";
  margin-left: -1em;
  color: rgb(255, 255, 255);
}

.icon-svgexport-3 .path9:before {
  content: "\e970";
  margin-left: -1em;
  color: rgb(0, 0, 0);
}

.icon-svgexport-3 .path10:before {
  content: "\e971";
  margin-left: -1em;
  color: rgb(255, 255, 255);
}

.icon-svgexport-3 .path11:before {
  content: "\e972";
  margin-left: -1em;
  color: rgb(0, 0, 0);
}

.icon-svgexport-3 .path12:before {
  content: "\e973";
  margin-left: -1em;
  color: rgb(255, 255, 255);
}

.icon-svgexport-3 .path13:before {
  content: "\e974";
  margin-left: -1em;
  color: rgb(255, 255, 255);
}

.icon-svgexport-3 .path14:before {
  content: "\e975";
  margin-left: -1em;
  color: rgb(255, 255, 255);
}

.icon-svgexport-3 .path15:before {
  content: "\e976";
  margin-left: -1em;
  color: rgb(255, 255, 255);
}

.icon-svgexport-3 .path16:before {
  content: "\e977";
  margin-left: -1em;
  color: rgb(255, 255, 255);
}

.icon-svgexport-3 .path17:before {
  content: "\e978";
  margin-left: -1em;
  color: rgb(255, 255, 255);
}

.icon-svgexport-3 .path18:before {
  content: "\e979";
  margin-left: -1em;
  color: rgb(255, 255, 255);
}

.icon-svgexport-3 .path19:before {
  content: "\e97a";
  margin-left: -1em;
  color: rgb(255, 255, 255);
}

.icon-svgexport-3 .path20:before {
  content: "\e97b";
  margin-left: -1em;
  color: rgb(255, 255, 255);
}

.icon-svgexport-3 .path21:before {
  content: "\e97c";
  margin-left: -1em;
  color: rgb(255, 255, 255);
}

.icon-svgexport-4 .path1:before {
  content: "\e97d";
  color: rgb(255, 233, 181);
}

.icon-svgexport-4 .path2:before {
  content: "\e97e";
  margin-left: -1em;
  color: rgb(0, 0, 0);
}

.icon-svgexport-6 .path1:before {
  content: "\e97f";
  color: rgb(255, 233, 181);
}

.icon-svgexport-6 .path2:before {
  content: "\e980";
  margin-left: -1em;
  color: rgb(255, 233, 181);
}

.icon-svgexport-6 .path3:before {
  content: "\e981";
  margin-left: -1em;
  color: rgb(255, 233, 181);
}

.icon-svgexport-6 .path4:before {
  content: "\e982";
  margin-left: -1em;
  color: rgb(255, 233, 181);
}

.icon-svgexport-6 .path5:before {
  content: "\e983";
  margin-left: -1em;
  color: rgb(0, 0, 0);
}

.icon-svgexport-6 .path6:before {
  content: "\e984";
  margin-left: -1em;
  color: rgb(255, 233, 181);
}

.icon-svgexport-6 .path7:before {
  content: "\e985";
  margin-left: -1em;
  color: rgb(0, 0, 0);
}

.icon-svgexport-6 .path8:before {
  content: "\e986";
  margin-left: -1em;
  color: rgb(0, 0, 0);
}

.icon-svgexport-6 .path9:before {
  content: "\e987";
  margin-left: -1em;
  color: rgb(255, 233, 181);
}

.icon-svgexport-6 .path10:before {
  content: "\e988";
  margin-left: -1em;
  color: rgb(255, 233, 181);
}

.icon-svgexport-6 .path11:before {
  content: "\e989";
  margin-left: -1em;
  color: rgb(0, 0, 0);
}

.icon-Vector:before {
  content: "\e98a";
}

.icon-Vector-1:before {
  content: "\e98b";
}

.icon-Vector-2:before {
  content: "\e98c";
}

.icon-Vector-3:before {
  content: "\e98d";
}

.icon-Vector-4:before {
  content: "\e98e";
}

.icon-Vector-5:before {
  content: "\e98f";
}

.icon-Vector-6:before {
  content: "\e990";
  color: #fd7923;
}

.icon-Vector-7:before {
  content: "\e991";
}

.icon-Vector-8:before {
  content: "\e992";
}

.icon-Vector-9:before {
  content: "\e993";
}

.icon-Vector-10:before {
  content: "\e994";
}

.icon-Vector-21:before {
  content: "\e995";
}
